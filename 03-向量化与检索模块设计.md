# 企业级RAG知识库系统 - 向量化与检索模块设计

## 1. 模块概述

向量化与检索模块是RAG系统的核心引擎，负责将文本转换为高维向量并提供高效的相似性检索服务。该模块采用混合检索策略，结合向量检索、关键词检索和重排序技术，确保检索结果的准确性和相关性。

### 1.1 核心功能

- **多模型向量化**：支持OpenAI、BGE、M3E等多种Embedding模型
- **混合检索**：向量检索 + 关键词检索 + 重排序
- **智能缓存**：Redis缓存热门查询和向量结果
- **批量处理**：支持大批量文档的异步向量化
- **增量更新**：文档变更时的增量向量化处理

### 1.2 技术架构

```mermaid
graph TB
    subgraph "向量化服务"
        VectorController[向量控制器]
        VectorManager[向量管理器]
        EmbeddingRouter[Embedding路由器]
        
        subgraph "Embedding模型"
            OpenAIEmbedding[OpenAI Embedding]
            BGEEmbedding[BGE模型]
            M3EEmbedding[M3E模型]
            LocalEmbedding[本地模型]
        end
        
        BatchProcessor[批量处理器]
        VectorCache[向量缓存]
    end
    
    subgraph "检索服务"
        SearchController[检索控制器]
        SearchManager[检索管理器]
        
        subgraph "检索策略"
            VectorSearch[向量检索]
            KeywordSearch[关键词检索]
            HybridSearch[混合检索]
        end
        
        Reranker[重排序器]
        ResultCache[结果缓存]
    end
    
    subgraph "存储层"
        Milvus[(Milvus向量库)]
        Redis[(Redis缓存)]
        MySQL[(元数据存储)]
    end
    
    subgraph "消息队列"
        RocketMQ[RocketMQ]
    end
    
    VectorController --> VectorManager
    VectorManager --> EmbeddingRouter
    EmbeddingRouter --> OpenAIEmbedding
    EmbeddingRouter --> BGEEmbedding
    EmbeddingRouter --> M3EEmbedding
    EmbeddingRouter --> LocalEmbedding
    
    VectorManager --> BatchProcessor
    VectorManager --> VectorCache
    VectorManager --> Milvus
    
    SearchController --> SearchManager
    SearchManager --> VectorSearch
    SearchManager --> KeywordSearch
    SearchManager --> HybridSearch
    
    VectorSearch --> Milvus
    KeywordSearch --> MySQL
    HybridSearch --> Reranker
    
    SearchManager --> ResultCache
    ResultCache --> Redis
    
    BatchProcessor --> RocketMQ
```

## 2. 向量化服务设计

### 2.1 向量化管理器

```java
@Service
@Slf4j
public class VectorManager {
    
    @Autowired
    private EmbeddingModelRouter embeddingRouter;
    
    @Autowired
    private MilvusService milvusService;
    
    @Autowired
    private VectorCacheService vectorCache;
    
    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    
    /**
     * 文本向量化主流程
     */
    public VectorizeResult vectorizeText(VectorizeRequest request) {
        try {
            String text = request.getText();
            String modelName = request.getModelName();
            
            // 1. 检查缓存
            String cacheKey = generateCacheKey(text, modelName);
            float[] cachedVector = vectorCache.getVector(cacheKey);
            if (cachedVector != null) {
                log.debug("命中向量缓存: {}", cacheKey);
                return VectorizeResult.success(cachedVector);
            }
            
            // 2. 调用Embedding模型
            EmbeddingModel model = embeddingRouter.getModel(modelName);
            float[] vector = model.embed(text);
            
            // 3. 缓存向量结果
            vectorCache.putVector(cacheKey, vector);
            
            // 4. 存储到Milvus
            VectorEntity entity = VectorEntity.builder()
                .id(request.getDocumentId())
                .vector(vector)
                .metadata(request.getMetadata())
                .build();
            
            milvusService.insert(entity);
            
            return VectorizeResult.success(vector);
            
        } catch (Exception e) {
            log.error("文本向量化失败: {}", request.getText(), e);
            return VectorizeResult.failure(e.getMessage());
        }
    }
    
    /**
     * 批量向量化处理
     */
    public void batchVectorize(List<TextChunk> chunks) {
        // 分批处理，避免内存溢出
        int batchSize = 100;
        List<List<TextChunk>> batches = Lists.partition(chunks, batchSize);
        
        for (List<TextChunk> batch : batches) {
            BatchVectorizeMessage message = BatchVectorizeMessage.builder()
                .chunks(batch)
                .timestamp(System.currentTimeMillis())
                .build();
            
            rocketMQTemplate.convertAndSend(
                RocketMQTopics.BATCH_VECTORIZE,
                message
            );
        }
    }
}
```

### 2.2 国产向量模型选型对比

| 模型名称 | 维度 | 精度(MTEB) | 速度(tokens/s) | 内存占用 | 适用场景 | 推荐度 |
|----------|------|------------|----------------|----------|----------|--------|
| **BGE-large-zh** | 1024 | 68.5% | 2000 | 1.3GB | 通用检索，高精度要求 | ⭐⭐⭐⭐⭐ |
| **BGE-base-zh** | 768 | 65.2% | 3500 | 400MB | 资源受限，平衡性能 | ⭐⭐⭐⭐ |
| **M3E-large** | 1024 | 66.8% | 1800 | 1.2GB | 多语言支持 | ⭐⭐⭐⭐ |
| **M3E-base** | 768 | 63.5% | 3200 | 380MB | 轻量级部署 | ⭐⭐⭐ |
| **text2vec-large** | 1024 | 64.1% | 1500 | 1.1GB | 中文优化 | ⭐⭐⭐ |
| **text2vec-base** | 768 | 61.8% | 2800 | 350MB | 快速原型 | ⭐⭐ |

### 2.3 向量化缓存方案

```java
@Service
@Slf4j
public class VectorCacheService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RedisScript<Boolean> cacheScript;

    private static final String VECTOR_CACHE_PREFIX = "vector:cache:";
    private static final String VECTOR_LOCK_PREFIX = "vector:lock:";
    private static final Duration VECTOR_CACHE_TTL = Duration.ofDays(7);
    private static final Duration LOCK_TTL = Duration.ofMinutes(5);

    public float[] getVector(String cacheKey) {
        try {
            String key = VECTOR_CACHE_PREFIX + cacheKey;

            // 使用Pipeline提升性能
            List<Object> results = redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                connection.exists(key.getBytes());
                connection.get(key.getBytes());
                return null;
            });

            Boolean exists = (Boolean) results.get(0);
            if (Boolean.TRUE.equals(exists)) {
                byte[] vectorBytes = (byte[]) results.get(1);
                if (vectorBytes != null) {
                    return deserializeVector(vectorBytes);
                }
            }

            return null;

        } catch (Exception e) {
            log.warn("获取向量缓存失败: {}", cacheKey, e);
            return null;
        }
    }

    public void putVector(String cacheKey, float[] vector) {
        try {
            String key = VECTOR_CACHE_PREFIX + cacheKey;
            byte[] vectorBytes = serializeVector(vector);

            redisTemplate.opsForValue().set(key, vectorBytes, VECTOR_CACHE_TTL);

        } catch (Exception e) {
            log.warn("存储向量缓存失败: {}", cacheKey, e);
        }
    }

    public boolean tryLock(String lockKey) {
        try {
            String key = VECTOR_LOCK_PREFIX + lockKey;
            Boolean success = redisTemplate.opsForValue()
                .setIfAbsent(key, "locked", LOCK_TTL);
            return Boolean.TRUE.equals(success);
        } catch (Exception e) {
            log.warn("获取向量锁失败: {}", lockKey, e);
            return false;
        }
    }

    public void releaseLock(String lockKey) {
        try {
            String key = VECTOR_LOCK_PREFIX + lockKey;
            redisTemplate.delete(key);
        } catch (Exception e) {
            log.warn("释放向量锁失败: {}", lockKey, e);
        }
    }

    private byte[] serializeVector(float[] vector) {
        ByteBuffer buffer = ByteBuffer.allocate(vector.length * 4);
        for (float value : vector) {
            buffer.putFloat(value);
        }
        return buffer.array();
    }

    private float[] deserializeVector(byte[] bytes) {
        ByteBuffer buffer = ByteBuffer.wrap(bytes);
        float[] vector = new float[bytes.length / 4];
        for (int i = 0; i < vector.length; i++) {
            vector[i] = buffer.getFloat();
        }
        return vector;
    }
}
```

### 2.4 Embedding模型路由器

```java
@Component
public class EmbeddingModelRouter {

    private final Map<String, EmbeddingModel> models;

    public EmbeddingModelRouter(List<EmbeddingModel> modelList) {
        this.models = modelList.stream()
            .collect(Collectors.toMap(
                EmbeddingModel::getModelName,
                Function.identity()
            ));
    }

    public EmbeddingModel getModel(String modelName) {
        EmbeddingModel model = models.get(modelName);
        if (model == null) {
            throw new UnsupportedModelException("不支持的Embedding模型: " + modelName);
        }
        return model;
    }

    public List<String> getSupportedModels() {
        return new ArrayList<>(models.keySet());
    }
}
```

### 2.5 增量更新方案

```java
@Service
@Slf4j
public class IncrementalVectorUpdateService {

    @Autowired
    private DocumentChangeDetector changeDetector;

    @Autowired
    private VectorManager vectorManager;

    @Autowired
    private MilvusService milvusService;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    public void processDocumentUpdate(String documentId) {
        try {
            // 1. 检测文档变更
            DocumentChangeResult changeResult = changeDetector.detectChanges(documentId);

            if (!changeResult.hasChanges()) {
                log.debug("文档无变更: {}", documentId);
                return;
            }

            // 2. 处理不同类型的变更
            switch (changeResult.getChangeType()) {
                case CONTENT_MODIFIED:
                    handleContentModification(documentId, changeResult);
                    break;
                case CHUNKS_ADDED:
                    handleChunksAddition(documentId, changeResult);
                    break;
                case CHUNKS_REMOVED:
                    handleChunksRemoval(documentId, changeResult);
                    break;
                case METADATA_UPDATED:
                    handleMetadataUpdate(documentId, changeResult);
                    break;
            }

        } catch (Exception e) {
            log.error("增量更新处理失败: {}", documentId, e);
            throw new IncrementalUpdateException("增量更新失败", e);
        }
    }

    private void handleContentModification(String documentId, DocumentChangeResult changeResult) {
        // 1. 获取变更的文本块
        List<TextChunk> modifiedChunks = changeResult.getModifiedChunks();

        // 2. 删除旧向量
        List<String> oldVectorIds = modifiedChunks.stream()
            .map(TextChunk::getVectorId)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        if (!oldVectorIds.isEmpty()) {
            milvusService.deleteVectors(oldVectorIds);
        }

        // 3. 生成新向量
        for (TextChunk chunk : modifiedChunks) {
            IncrementalVectorizeMessage message = IncrementalVectorizeMessage.builder()
                .documentId(documentId)
                .chunkId(chunk.getId())
                .content(chunk.getContent())
                .operation(VectorOperation.UPDATE)
                .priority(MessagePriority.HIGH)
                .build();

            rocketMQTemplate.convertAndSend(
                RocketMQTopics.INCREMENTAL_VECTORIZE,
                message
            );
        }
    }

    private void handleChunksAddition(String documentId, DocumentChangeResult changeResult) {
        List<TextChunk> newChunks = changeResult.getAddedChunks();

        for (TextChunk chunk : newChunks) {
            IncrementalVectorizeMessage message = IncrementalVectorizeMessage.builder()
                .documentId(documentId)
                .chunkId(chunk.getId())
                .content(chunk.getContent())
                .operation(VectorOperation.INSERT)
                .priority(MessagePriority.NORMAL)
                .build();

            rocketMQTemplate.convertAndSend(
                RocketMQTopics.INCREMENTAL_VECTORIZE,
                message
            );
        }
    }

    private void handleChunksRemoval(String documentId, DocumentChangeResult changeResult) {
        List<String> removedVectorIds = changeResult.getRemovedChunks().stream()
            .map(TextChunk::getVectorId)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        if (!removedVectorIds.isEmpty()) {
            milvusService.deleteVectors(removedVectorIds);
        }
    }

    private void handleMetadataUpdate(String documentId, DocumentChangeResult changeResult) {
        // 元数据更新通常不需要重新向量化，只需更新Milvus中的元数据
        List<VectorMetadataUpdate> updates = changeResult.getMetadataUpdates();
        milvusService.updateMetadata(updates);
    }
}

@Component
public class DocumentChangeDetector {

    @Autowired
    private TextChunkService textChunkService;

    public DocumentChangeResult detectChanges(String documentId) {
        // 1. 获取当前文档的文本块
        List<TextChunk> currentChunks = textChunkService.getChunksByDocumentId(documentId);

        // 2. 获取上次处理时的文本块快照
        List<TextChunk> previousChunks = textChunkService.getPreviousSnapshot(documentId);

        // 3. 比较变更
        return compareChunks(currentChunks, previousChunks);
    }

    private DocumentChangeResult compareChunks(List<TextChunk> current, List<TextChunk> previous) {
        DocumentChangeResult.Builder resultBuilder = DocumentChangeResult.builder();

        Map<String, TextChunk> currentMap = current.stream()
            .collect(Collectors.toMap(TextChunk::getId, Function.identity()));

        Map<String, TextChunk> previousMap = previous.stream()
            .collect(Collectors.toMap(TextChunk::getId, Function.identity()));

        // 检测新增的块
        List<TextChunk> addedChunks = current.stream()
            .filter(chunk -> !previousMap.containsKey(chunk.getId()))
            .collect(Collectors.toList());

        // 检测删除的块
        List<TextChunk> removedChunks = previous.stream()
            .filter(chunk -> !currentMap.containsKey(chunk.getId()))
            .collect(Collectors.toList());

        // 检测修改的块
        List<TextChunk> modifiedChunks = current.stream()
            .filter(chunk -> {
                TextChunk prevChunk = previousMap.get(chunk.getId());
                return prevChunk != null && !Objects.equals(chunk.getContentHash(), prevChunk.getContentHash());
            })
            .collect(Collectors.toList());

        return resultBuilder
            .addedChunks(addedChunks)
            .removedChunks(removedChunks)
            .modifiedChunks(modifiedChunks)
            .changeType(determineChangeType(addedChunks, removedChunks, modifiedChunks))
            .build();
    }

    private ChangeType determineChangeType(List<TextChunk> added, List<TextChunk> removed, List<TextChunk> modified) {
        if (!modified.isEmpty()) {
            return ChangeType.CONTENT_MODIFIED;
        } else if (!added.isEmpty()) {
            return ChangeType.CHUNKS_ADDED;
        } else if (!removed.isEmpty()) {
            return ChangeType.CHUNKS_REMOVED;
        } else {
            return ChangeType.NO_CHANGE;
        }
    }
}
```

### 2.6 BGE Embedding模型实现

```java
@Component
@Slf4j
public class BGEEmbeddingModel implements EmbeddingModel {
    
    @Autowired
    private EmbeddingServiceClient embeddingClient;
    
    @Value("${embedding.bge.model-name:bge-large-zh}")
    private String modelName;
    
    @Value("${embedding.bge.max-tokens:512}")
    private int maxTokens;
    
    @Override
    public String getModelName() {
        return "bge-large-zh";
    }
    
    @Override
    public int getDimension() {
        return 1024;
    }
    
    @Override
    public float[] embed(String text) {
        try {
            // 文本预处理
            String processedText = preprocessText(text);
            
            // 调用Embedding服务
            EmbeddingRequest request = EmbeddingRequest.builder()
                .text(processedText)
                .model(modelName)
                .maxTokens(maxTokens)
                .build();
            
            EmbeddingResponse response = embeddingClient.embed(request);
            
            if (response.isSuccess()) {
                return response.getVector();
            } else {
                throw new EmbeddingException("BGE模型调用失败: " + response.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("BGE Embedding失败: {}", text, e);
            throw new EmbeddingException("BGE Embedding处理失败", e);
        }
    }
    
    private String preprocessText(String text) {
        // 文本长度截断
        if (text.length() > maxTokens * 4) { // 粗略估算token数
            text = text.substring(0, maxTokens * 4);
        }
        
        // 清理特殊字符
        return text.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]", "")
                  .trim();
    }
}
```

## 3. Milvus集成设计

### 3.1 Milvus服务封装

```java
@Service
@Slf4j
public class MilvusService {
    
    @Autowired
    private MilvusClient milvusClient;
    
    @Value("${milvus.collection.name:rag_vectors}")
    private String collectionName;
    
    @Value("${milvus.index.type:IVF_FLAT}")
    private String indexType;
    
    /**
     * 初始化集合
     */
    @PostConstruct
    public void initCollection() {
        try {
            if (!hasCollection()) {
                createCollection();
                createIndex();
            }
        } catch (Exception e) {
            log.error("Milvus集合初始化失败", e);
            throw new MilvusException("Milvus初始化失败", e);
        }
    }
    
    /**
     * 插入向量
     */
    public void insert(VectorEntity entity) {
        try {
            List<InsertParam.Field> fields = Arrays.asList(
                new InsertParam.Field("id", Collections.singletonList(entity.getId())),
                new InsertParam.Field("vector", Collections.singletonList(entity.getVector())),
                new InsertParam.Field("document_id", Collections.singletonList(entity.getDocumentId())),
                new InsertParam.Field("chunk_id", Collections.singletonList(entity.getChunkId())),
                new InsertParam.Field("content", Collections.singletonList(entity.getContent())),
                new InsertParam.Field("metadata", Collections.singletonList(entity.getMetadata()))
            );
            
            InsertParam insertParam = InsertParam.newBuilder()
                .withCollectionName(collectionName)
                .withFields(fields)
                .build();
            
            R<MutationResult> response = milvusClient.insert(insertParam);
            
            if (response.getStatus() != R.Status.Success.getCode()) {
                throw new MilvusException("向量插入失败: " + response.getMessage());
            }
            
            log.debug("向量插入成功: {}", entity.getId());
            
        } catch (Exception e) {
            log.error("向量插入异常: {}", entity.getId(), e);
            throw new MilvusException("向量插入失败", e);
        }
    }
    
    /**
     * 向量检索
     */
    public List<SearchResult> search(SearchRequest request) {
        try {
            List<String> searchOutputFields = Arrays.asList(
                "id", "document_id", "chunk_id", "content", "metadata"
            );
            
            SearchParam searchParam = SearchParam.newBuilder()
                .withCollectionName(collectionName)
                .withMetricType(MetricType.COSINE)
                .withOutFields(searchOutputFields)
                .withTopK(request.getTopK())
                .withVectors(Collections.singletonList(request.getQueryVector()))
                .withVectorFieldName("vector")
                .withParams(request.getSearchParams())
                .build();
            
            R<SearchResults> response = milvusClient.search(searchParam);
            
            if (response.getStatus() != R.Status.Success.getCode()) {
                throw new MilvusException("向量检索失败: " + response.getMessage());
            }
            
            return convertSearchResults(response.getData());
            
        } catch (Exception e) {
            log.error("向量检索异常", e);
            throw new MilvusException("向量检索失败", e);
        }
    }
    
    private void createCollection() {
        List<FieldType> fields = Arrays.asList(
            FieldType.newBuilder()
                .withName("id")
                .withDataType(DataType.Int64)
                .withPrimaryKey(true)
                .withAutoID(true)
                .build(),
            FieldType.newBuilder()
                .withName("vector")
                .withDataType(DataType.FloatVector)
                .withDimension(1024)
                .build(),
            FieldType.newBuilder()
                .withName("document_id")
                .withDataType(DataType.VarChar)
                .withMaxLength(64)
                .build(),
            FieldType.newBuilder()
                .withName("chunk_id")
                .withDataType(DataType.VarChar)
                .withMaxLength(64)
                .build(),
            FieldType.newBuilder()
                .withName("content")
                .withDataType(DataType.VarChar)
                .withMaxLength(65535)
                .build(),
            FieldType.newBuilder()
                .withName("metadata")
                .withDataType(DataType.JSON)
                .build()
        );
        
        CreateCollectionParam createParam = CreateCollectionParam.newBuilder()
            .withCollectionName(collectionName)
            .withDescription("RAG向量集合")
            .withShardsNum(2)
            .withFieldTypes(fields)
            .build();
        
        R<RpcStatus> response = milvusClient.createCollection(createParam);
        
        if (response.getStatus() != R.Status.Success.getCode()) {
            throw new MilvusException("创建集合失败: " + response.getMessage());
        }
    }
}
```

## 4. 混合检索设计

### 4.1 检索管理器

```java
@Service
@Slf4j
public class SearchManager {
    
    @Autowired
    private VectorSearchService vectorSearchService;
    
    @Autowired
    private KeywordSearchService keywordSearchService;
    
    @Autowired
    private RerankerService rerankerService;
    
    @Autowired
    private SearchCacheService searchCache;
    
    /**
     * 混合检索主流程
     */
    public SearchResponse hybridSearch(SearchRequest request) {
        try {
            String query = request.getQuery();
            
            // 1. 检查缓存
            String cacheKey = generateSearchCacheKey(request);
            SearchResponse cachedResult = searchCache.getSearchResult(cacheKey);
            if (cachedResult != null) {
                log.debug("命中检索缓存: {}", cacheKey);
                return cachedResult;
            }
            
            // 2. 并行执行向量检索和关键词检索
            CompletableFuture<List<SearchResult>> vectorFuture = 
                CompletableFuture.supplyAsync(() -> vectorSearchService.search(request));
            
            CompletableFuture<List<SearchResult>> keywordFuture = 
                CompletableFuture.supplyAsync(() -> keywordSearchService.search(request));
            
            // 3. 等待检索结果
            List<SearchResult> vectorResults = vectorFuture.get();
            List<SearchResult> keywordResults = keywordFuture.get();
            
            // 4. 结果融合
            List<SearchResult> fusedResults = fuseResults(vectorResults, keywordResults);
            
            // 5. 重排序
            List<SearchResult> rerankedResults = rerankerService.rerank(query, fusedResults);
            
            // 6. 构建响应
            SearchResponse response = SearchResponse.builder()
                .query(query)
                .results(rerankedResults)
                .totalCount(rerankedResults.size())
                .searchTime(System.currentTimeMillis() - request.getStartTime())
                .build();
            
            // 7. 缓存结果
            searchCache.putSearchResult(cacheKey, response);
            
            return response;
            
        } catch (Exception e) {
            log.error("混合检索失败: {}", request.getQuery(), e);
            throw new SearchException("检索处理失败", e);
        }
    }
    
    /**
     * 结果融合算法（RRF - Reciprocal Rank Fusion）
     */
    private List<SearchResult> fuseResults(List<SearchResult> vectorResults, 
                                          List<SearchResult> keywordResults) {
        Map<String, SearchResult> resultMap = new HashMap<>();
        Map<String, Double> scoreMap = new HashMap<>();
        
        // 处理向量检索结果
        for (int i = 0; i < vectorResults.size(); i++) {
            SearchResult result = vectorResults.get(i);
            String id = result.getId();
            double rrfScore = 1.0 / (i + 1 + 60); // RRF公式，k=60
            
            resultMap.put(id, result);
            scoreMap.put(id, scoreMap.getOrDefault(id, 0.0) + rrfScore * 0.7); // 向量权重0.7
        }
        
        // 处理关键词检索结果
        for (int i = 0; i < keywordResults.size(); i++) {
            SearchResult result = keywordResults.get(i);
            String id = result.getId();
            double rrfScore = 1.0 / (i + 1 + 60);
            
            if (!resultMap.containsKey(id)) {
                resultMap.put(id, result);
            }
            scoreMap.put(id, scoreMap.getOrDefault(id, 0.0) + rrfScore * 0.3); // 关键词权重0.3
        }
        
        // 按融合分数排序
        return scoreMap.entrySet().stream()
            .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
            .map(entry -> {
                SearchResult result = resultMap.get(entry.getKey());
                result.setFusionScore(entry.getValue());
                return result;
            })
            .collect(Collectors.toList());
    }
}
```

### 4.2 重排序服务

```java
@Service
@Slf4j
public class RerankerService {
    
    @Autowired
    private CrossEncoderClient crossEncoderClient;
    
    @Value("${reranker.model:bge-reranker-large}")
    private String rerankerModel;
    
    @Value("${reranker.top-k:10}")
    private int topK;
    
    /**
     * 重排序处理
     */
    public List<SearchResult> rerank(String query, List<SearchResult> results) {
        if (results.size() <= 1) {
            return results;
        }
        
        try {
            // 构建查询-文档对
            List<QueryDocumentPair> pairs = results.stream()
                .map(result -> QueryDocumentPair.builder()
                    .query(query)
                    .document(result.getContent())
                    .resultId(result.getId())
                    .build())
                .collect(Collectors.toList());
            
            // 调用重排序模型
            RerankRequest request = RerankRequest.builder()
                .model(rerankerModel)
                .pairs(pairs)
                .topK(Math.min(topK, results.size()))
                .build();
            
            RerankResponse response = crossEncoderClient.rerank(request);
            
            if (response.isSuccess()) {
                // 根据重排序分数重新排列结果
                Map<String, SearchResult> resultMap = results.stream()
                    .collect(Collectors.toMap(SearchResult::getId, Function.identity()));
                
                return response.getRankedResults().stream()
                    .map(rankedResult -> {
                        SearchResult result = resultMap.get(rankedResult.getResultId());
                        result.setRerankScore(rankedResult.getScore());
                        return result;
                    })
                    .collect(Collectors.toList());
            } else {
                log.warn("重排序失败，返回原始结果: {}", response.getErrorMessage());
                return results.subList(0, Math.min(topK, results.size()));
            }
            
        } catch (Exception e) {
            log.error("重排序处理异常，返回原始结果", e);
            return results.subList(0, Math.min(topK, results.size()));
        }
    }
}
```

## 5. 缓存策略设计

### 5.1 向量缓存服务

```java
@Service
@Slf4j
public class VectorCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String VECTOR_CACHE_PREFIX = "vector:cache:";
    private static final Duration VECTOR_CACHE_TTL = Duration.ofDays(7);
    
    public float[] getVector(String cacheKey) {
        try {
            String key = VECTOR_CACHE_PREFIX + cacheKey;
            Object cached = redisTemplate.opsForValue().get(key);
            
            if (cached instanceof float[]) {
                return (float[]) cached;
            }
            
            return null;
            
        } catch (Exception e) {
            log.warn("获取向量缓存失败: {}", cacheKey, e);
            return null;
        }
    }
    
    public void putVector(String cacheKey, float[] vector) {
        try {
            String key = VECTOR_CACHE_PREFIX + cacheKey;
            redisTemplate.opsForValue().set(key, vector, VECTOR_CACHE_TTL);
            
        } catch (Exception e) {
            log.warn("存储向量缓存失败: {}", cacheKey, e);
        }
    }
}
```

### 5.2 检索结果缓存

```java
@Service
@Slf4j
public class SearchCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String SEARCH_CACHE_PREFIX = "search:cache:";
    private static final Duration SEARCH_CACHE_TTL = Duration.ofHours(1);
    
    public SearchResponse getSearchResult(String cacheKey) {
        try {
            String key = SEARCH_CACHE_PREFIX + cacheKey;
            return (SearchResponse) redisTemplate.opsForValue().get(key);
            
        } catch (Exception e) {
            log.warn("获取检索缓存失败: {}", cacheKey, e);
            return null;
        }
    }
    
    public void putSearchResult(String cacheKey, SearchResponse response) {
        try {
            String key = SEARCH_CACHE_PREFIX + cacheKey;
            redisTemplate.opsForValue().set(key, response, SEARCH_CACHE_TTL);
            
        } catch (Exception e) {
            log.warn("存储检索缓存失败: {}", cacheKey, e);
        }
    }
}
```

## 6. QA问答对管理

### 6.1 QA对数据结构设计

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("rag_qa_pair")
public class QAPair {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    @TableField("knowledge_base_id")
    private String knowledgeBaseId;

    @TableField("question")
    private String question;

    @TableField("question_hash")
    private String questionHash;

    @TableField("answer")
    private String answer;

    @TableField("answer_type")
    @EnumValue
    private AnswerType answerType; // MANUAL, GENERATED, VERIFIED

    @TableField("confidence_score")
    private Double confidenceScore;

    @TableField("usage_count")
    private Integer usageCount;

    @TableField("positive_feedback")
    private Integer positiveFeedback;

    @TableField("negative_feedback")
    private Integer negativeFeedback;

    @TableField("source_documents")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> sourceDocuments;

    @TableField("tags")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> tags;

    @TableField("metadata")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> metadata;

    @TableField("vector_id")
    private String vectorId;

    @TableField("embedding_model")
    private String embeddingModel;

    @TableField("status")
    @EnumValue
    private QAStatus status; // ACTIVE, INACTIVE, PENDING_REVIEW

    @TableField("created_by")
    private String createdBy;

    @TableField("updated_by")
    private String updatedBy;

    @TableField("created_time")
    private LocalDateTime createdTime;

    @TableField("updated_time")
    private LocalDateTime updatedTime;
}

@Service
@Slf4j
public class QAPairService {

    @Autowired
    private QAPairMapper qaPairMapper;

    @Autowired
    private VectorManager vectorManager;

    @Autowired
    private QAPairCacheService cacheService;

    public QAPair createQAPair(CreateQAPairRequest request) {
        try {
            // 1. 构建QA对象
            QAPair qaPair = QAPair.builder()
                .knowledgeBaseId(request.getKnowledgeBaseId())
                .question(request.getQuestion())
                .questionHash(DigestUtils.sha256Hex(request.getQuestion()))
                .answer(request.getAnswer())
                .answerType(request.getAnswerType())
                .confidenceScore(request.getConfidenceScore())
                .sourceDocuments(request.getSourceDocuments())
                .tags(request.getTags())
                .metadata(request.getMetadata())
                .status(QAStatus.ACTIVE)
                .createdBy(request.getCreatedBy())
                .createdTime(LocalDateTime.now())
                .build();

            // 2. 生成问题向量
            VectorizeRequest vectorRequest = VectorizeRequest.builder()
                .text(request.getQuestion())
                .modelName("bge-large-zh")
                .build();

            VectorizeResult vectorResult = vectorManager.vectorizeText(vectorRequest);
            if (vectorResult.isSuccess()) {
                qaPair.setVectorId(vectorResult.getVectorId());
                qaPair.setEmbeddingModel("bge-large-zh");
            }

            // 3. 保存到数据库
            qaPairMapper.insert(qaPair);

            // 4. 清除相关缓存
            cacheService.evictQACache(request.getKnowledgeBaseId());

            return qaPair;

        } catch (Exception e) {
            log.error("创建QA对失败", e);
            throw new QAPairException("创建QA对失败", e);
        }
    }

    public List<QAPair> searchSimilarQA(String question, String knowledgeBaseId, int topK) {
        try {
            // 1. 检查缓存
            String cacheKey = generateSearchCacheKey(question, knowledgeBaseId, topK);
            List<QAPair> cachedResults = cacheService.getCachedQAResults(cacheKey);
            if (cachedResults != null) {
                return cachedResults;
            }

            // 2. 向量化问题
            VectorizeRequest vectorRequest = VectorizeRequest.builder()
                .text(question)
                .modelName("bge-large-zh")
                .build();

            VectorizeResult vectorResult = vectorManager.vectorizeText(vectorRequest);
            if (!vectorResult.isSuccess()) {
                return Collections.emptyList();
            }

            // 3. 向量检索
            SearchRequest searchRequest = SearchRequest.builder()
                .queryVector(vectorResult.getVector())
                .topK(topK * 2) // 检索更多候选
                .threshold(0.7)
                .filters(Map.of("knowledge_base_id", knowledgeBaseId))
                .build();

            List<SearchResult> searchResults = milvusService.search(searchRequest);

            // 4. 转换为QA对象
            List<String> qaIds = searchResults.stream()
                .map(SearchResult::getId)
                .collect(Collectors.toList());

            List<QAPair> qaPairs = qaPairMapper.selectBatchIds(qaIds);

            // 5. 按相似度排序并限制数量
            List<QAPair> sortedResults = qaPairs.stream()
                .sorted((qa1, qa2) -> {
                    double score1 = getSearchScore(qa1.getId(), searchResults);
                    double score2 = getSearchScore(qa2.getId(), searchResults);
                    return Double.compare(score2, score1);
                })
                .limit(topK)
                .collect(Collectors.toList());

            // 6. 缓存结果
            cacheService.cacheQAResults(cacheKey, sortedResults);

            return sortedResults;

        } catch (Exception e) {
            log.error("QA检索失败: {}", question, e);
            return Collections.emptyList();
        }
    }

    public void updateQAFeedback(String qaId, FeedbackType feedbackType) {
        try {
            QAPair qaPair = qaPairMapper.selectById(qaId);
            if (qaPair == null) {
                throw new QAPairNotFoundException("QA对不存在: " + qaId);
            }

            // 更新反馈统计
            if (feedbackType == FeedbackType.POSITIVE) {
                qaPair.setPositiveFeedback(qaPair.getPositiveFeedback() + 1);
            } else {
                qaPair.setNegativeFeedback(qaPair.getNegativeFeedback() + 1);
            }

            // 重新计算置信度
            double newConfidence = calculateConfidenceScore(qaPair);
            qaPair.setConfidenceScore(newConfidence);

            qaPairMapper.updateById(qaPair);

            // 清除缓存
            cacheService.evictQACache(qaPair.getKnowledgeBaseId());

        } catch (Exception e) {
            log.error("更新QA反馈失败: {}", qaId, e);
            throw new QAPairException("更新反馈失败", e);
        }
    }

    private double calculateConfidenceScore(QAPair qaPair) {
        int total = qaPair.getPositiveFeedback() + qaPair.getNegativeFeedback();
        if (total == 0) {
            return qaPair.getConfidenceScore();
        }

        double positiveRate = (double) qaPair.getPositiveFeedback() / total;
        double usageWeight = Math.min(qaPair.getUsageCount() / 100.0, 1.0);

        return positiveRate * 0.7 + usageWeight * 0.3;
    }

    private double getSearchScore(String qaId, List<SearchResult> searchResults) {
        return searchResults.stream()
            .filter(result -> result.getId().equals(qaId))
            .mapToDouble(SearchResult::getScore)
            .findFirst()
            .orElse(0.0);
    }

    private String generateSearchCacheKey(String question, String knowledgeBaseId, int topK) {
        String content = question + ":" + knowledgeBaseId + ":" + topK;
        return DigestUtils.sha256Hex(content);
    }
}
```

## 7. 问题预处理模块

### 7.1 问题摘要提取

```java
@Component
@Slf4j
public class QuestionSummaryExtractor {

    @Autowired
    private LLMModelRouter llmRouter;

    public String extractQuestionSummary(String question, SummaryConfig config) {
        try {
            if (question.length() <= config.getMinLengthForSummary()) {
                return question; // 短问题无需摘要
            }

            String prompt = buildQuestionSummaryPrompt(question, config);

            LLMRequest request = LLMRequest.builder()
                .prompt(prompt)
                .maxTokens(config.getMaxTokens())
                .temperature(0.1) // 低温度确保一致性
                .build();

            LLMModel model = llmRouter.selectModel(config.getModelName());
            LLMResponse response = model.generate(request);

            if (response.isSuccess()) {
                return response.getContent().trim();
            } else {
                log.warn("问题摘要提取失败，返回原问题: {}", response.getErrorMessage());
                return question;
            }

        } catch (Exception e) {
            log.error("问题摘要提取异常: {}", question, e);
            return question;
        }
    }

    private String buildQuestionSummaryPrompt(String question, SummaryConfig config) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("你是一个专业的问题分析专家。请将以下长问题提炼为简洁的核心问题。\n\n");
        prompt.append("要求：\n");
        prompt.append("- 保留问题的核心意图和关键信息\n");
        prompt.append("- 去除冗余的描述和背景信息\n");
        prompt.append("- 确保提炼后的问题仍然清晰明确\n");
        prompt.append("- 长度控制在").append(config.getMaxLength()).append("字以内\n\n");

        prompt.append("原问题：\n");
        prompt.append(question).append("\n\n");

        prompt.append("请提炼核心问题：");

        return prompt.toString();
    }
}
```

### 7.2 问题关键词提取

```java
@Component
@Slf4j
public class QuestionKeywordExtractor {

    @Autowired
    private LLMModelRouter llmRouter;

    @Autowired
    private KeywordCacheService keywordCache;

    public List<QuestionKeyword> extractKeywords(String question, KeywordConfig config) {
        try {
            // 1. 检查缓存
            String cacheKey = generateCacheKey(question, config);
            List<QuestionKeyword> cachedKeywords = keywordCache.getCachedKeywords(cacheKey);
            if (cachedKeywords != null) {
                return cachedKeywords;
            }

            // 2. 构建提示词
            String prompt = buildKeywordExtractionPrompt(question, config);

            // 3. 调用LLM
            LLMRequest request = LLMRequest.builder()
                .prompt(prompt)
                .maxTokens(config.getMaxTokens())
                .temperature(0.1)
                .build();

            LLMModel model = llmRouter.selectModel(config.getModelName());
            LLMResponse response = model.generate(request);

            if (response.isSuccess()) {
                List<QuestionKeyword> keywords = parseKeywordsFromResponse(response.getContent());

                // 4. 缓存结果
                keywordCache.cacheKeywords(cacheKey, keywords);

                return keywords;
            } else {
                log.warn("问题关键词提取失败: {}", response.getErrorMessage());
                return Collections.emptyList();
            }

        } catch (Exception e) {
            log.error("问题关键词提取异常: {}", question, e);
            return Collections.emptyList();
        }
    }

    private String buildKeywordExtractionPrompt(String question, KeywordConfig config) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("你是一个专业的问题分析专家。请从以下问题中提取关键词。\n\n");
        prompt.append("提取要求：\n");
        prompt.append("- 提取").append(config.getMaxKeywords()).append("个最重要的关键词\n");
        prompt.append("- 包括核心概念、实体、动作、属性等\n");
        prompt.append("- 按重要性排序\n");
        prompt.append("- 每个关键词1-6个字符\n\n");

        prompt.append("输出格式（JSON）：\n");
        prompt.append("{\n");
        prompt.append("  \"keywords\": [\n");
        prompt.append("    {\n");
        prompt.append("      \"text\": \"关键词\",\n");
        prompt.append("      \"type\": \"CONCEPT|ENTITY|ACTION|ATTRIBUTE\",\n");
        prompt.append("      \"importance\": 0.95,\n");
        prompt.append("      \"explanation\": \"选择原因\"\n");
        prompt.append("    }\n");
        prompt.append("  ]\n");
        prompt.append("}\n\n");

        prompt.append("问题：").append(question).append("\n\n");
        prompt.append("请提取关键词：");

        return prompt.toString();
    }

    private List<QuestionKeyword> parseKeywordsFromResponse(String response) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(response);

            List<QuestionKeyword> keywords = new ArrayList<>();

            if (rootNode.has("keywords") && rootNode.get("keywords").isArray()) {
                ArrayNode keywordsArray = (ArrayNode) rootNode.get("keywords");

                for (JsonNode keywordNode : keywordsArray) {
                    QuestionKeyword keyword = QuestionKeyword.builder()
                        .text(keywordNode.get("text").asText())
                        .type(KeywordType.valueOf(keywordNode.get("type").asText()))
                        .importance(keywordNode.get("importance").asDouble())
                        .explanation(keywordNode.get("explanation").asText())
                        .build();

                    keywords.add(keyword);
                }
            }

            return keywords;

        } catch (Exception e) {
            log.warn("解析关键词JSON失败，尝试文本解析: {}", e.getMessage());
            return parseKeywordsFromText(response);
        }
    }

    private List<QuestionKeyword> parseKeywordsFromText(String text) {
        List<QuestionKeyword> keywords = new ArrayList<>();

        String[] lines = text.split("\n");
        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;

            // 移除序号和特殊字符
            line = line.replaceAll("^\\d+[.、]\\s*", "")
                      .replaceAll("^[•\\-*]\\s*", "")
                      .trim();

            if (line.length() > 0 && line.length() <= 10) {
                QuestionKeyword keyword = QuestionKeyword.builder()
                    .text(line)
                    .type(KeywordType.CONCEPT)
                    .importance(0.7)
                    .explanation("文本解析提取")
                    .build();

                keywords.add(keyword);
            }
        }

        return keywords;
    }

    private String generateCacheKey(String question, KeywordConfig config) {
        String content = question + ":" + config.getModelName() + ":" + config.getMaxKeywords();
        return DigestUtils.sha256Hex(content);
    }
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionKeyword {
    private String text;
    private KeywordType type;
    private Double importance;
    private String explanation;
}
```

### 7.3 问题意图识别

```java
@Component
@Slf4j
public class QuestionIntentClassifier {

    @Autowired
    private LLMModelRouter llmRouter;

    @Autowired
    private IntentCacheService intentCache;

    public QuestionIntent classifyIntent(String question, IntentConfig config) {
        try {
            // 1. 检查缓存
            String cacheKey = generateIntentCacheKey(question);
            QuestionIntent cachedIntent = intentCache.getCachedIntent(cacheKey);
            if (cachedIntent != null) {
                return cachedIntent;
            }

            // 2. 构建分类提示词
            String prompt = buildIntentClassificationPrompt(question, config);

            // 3. 调用LLM
            LLMRequest request = LLMRequest.builder()
                .prompt(prompt)
                .maxTokens(config.getMaxTokens())
                .temperature(0.1)
                .build();

            LLMModel model = llmRouter.selectModel(config.getModelName());
            LLMResponse response = model.generate(request);

            if (response.isSuccess()) {
                QuestionIntent intent = parseIntentFromResponse(response.getContent());

                // 4. 缓存结果
                intentCache.cacheIntent(cacheKey, intent);

                return intent;
            } else {
                log.warn("问题意图识别失败: {}", response.getErrorMessage());
                return QuestionIntent.getDefault();
            }

        } catch (Exception e) {
            log.error("问题意图识别异常: {}", question, e);
            return QuestionIntent.getDefault();
        }
    }

    private String buildIntentClassificationPrompt(String question, IntentConfig config) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("你是一个专业的问题意图分析专家。请分析以下问题的意图类型。\n\n");

        prompt.append("意图类型定义：\n");
        prompt.append("- FACTUAL: 事实性问题，寻求具体信息或数据\n");
        prompt.append("- ANALYTICAL: 分析性问题，需要分析、比较、评估\n");
        prompt.append("- PROCEDURAL: 程序性问题，询问如何操作或步骤\n");
        prompt.append("- CREATIVE: 创造性问题，需要生成新想法或方案\n");
        prompt.append("- EXPLANATORY: 解释性问题，需要说明原理或原因\n");
        prompt.append("- COMPARATIVE: 比较性问题，对比不同选项\n");
        prompt.append("- TROUBLESHOOTING: 故障排除，解决问题\n\n");

        prompt.append("输出格式（JSON）：\n");
        prompt.append("{\n");
        prompt.append("  \"primary_intent\": \"FACTUAL\",\n");
        prompt.append("  \"confidence\": 0.95,\n");
        prompt.append("  \"secondary_intents\": [\"EXPLANATORY\"],\n");
        prompt.append("  \"complexity\": \"SIMPLE|MEDIUM|COMPLEX\",\n");
        prompt.append("  \"domain\": \"技术|商业|学术|生活\",\n");
        prompt.append("  \"reasoning\": \"分析原因\"\n");
        prompt.append("}\n\n");

        prompt.append("问题：").append(question).append("\n\n");
        prompt.append("请分析意图：");

        return prompt.toString();
    }

    private QuestionIntent parseIntentFromResponse(String response) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(response);

            IntentType primaryIntent = IntentType.valueOf(rootNode.get("primary_intent").asText());
            double confidence = rootNode.get("confidence").asDouble();

            List<IntentType> secondaryIntents = new ArrayList<>();
            if (rootNode.has("secondary_intents") && rootNode.get("secondary_intents").isArray()) {
                ArrayNode secondaryArray = (ArrayNode) rootNode.get("secondary_intents");
                for (JsonNode intentNode : secondaryArray) {
                    secondaryIntents.add(IntentType.valueOf(intentNode.asText()));
                }
            }

            ComplexityLevel complexity = ComplexityLevel.valueOf(rootNode.get("complexity").asText());
            String domain = rootNode.get("domain").asText();
            String reasoning = rootNode.get("reasoning").asText();

            return QuestionIntent.builder()
                .primaryIntent(primaryIntent)
                .confidence(confidence)
                .secondaryIntents(secondaryIntents)
                .complexity(complexity)
                .domain(domain)
                .reasoning(reasoning)
                .build();

        } catch (Exception e) {
            log.warn("解析意图JSON失败: {}", e.getMessage());
            return QuestionIntent.getDefault();
        }
    }

    private String generateIntentCacheKey(String question) {
        return DigestUtils.sha256Hex("intent:" + question);
    }
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionIntent {
    private IntentType primaryIntent;
    private Double confidence;
    private List<IntentType> secondaryIntents;
    private ComplexityLevel complexity;
    private String domain;
    private String reasoning;

    public static QuestionIntent getDefault() {
        return QuestionIntent.builder()
            .primaryIntent(IntentType.FACTUAL)
            .confidence(0.5)
            .secondaryIntents(Collections.emptyList())
            .complexity(ComplexityLevel.MEDIUM)
            .domain("通用")
            .reasoning("默认分类")
            .build();
    }
}

public enum IntentType {
    FACTUAL("事实性"),
    ANALYTICAL("分析性"),
    PROCEDURAL("程序性"),
    CREATIVE("创造性"),
    EXPLANATORY("解释性"),
    COMPARATIVE("比较性"),
    TROUBLESHOOTING("故障排除");

    private final String description;

    IntentType(String description) {
        this.description = description;
    }
}

public enum ComplexityLevel {
    SIMPLE("简单"),
    MEDIUM("中等"),
    COMPLEX("复杂");

    private final String description;

    ComplexityLevel(String description) {
        this.description = description;
    }
}
```

## 8. 多数据源召回策略

### 8.1 数据源统一接口设计

```java
public interface DataSource {
    String getSourceType();
    List<RetrievalResult> retrieve(RetrievalRequest request);
    boolean isAvailable();
    double getReliabilityScore();
}

@Component
@Slf4j
public class DocumentDataSource implements DataSource {

    @Autowired
    private MilvusService milvusService;

    @Autowired
    private VectorManager vectorManager;

    @Override
    public String getSourceType() {
        return "DOCUMENT";
    }

    @Override
    public List<RetrievalResult> retrieve(RetrievalRequest request) {
        try {
            // 1. 向量化查询
            VectorizeResult vectorResult = vectorManager.vectorizeText(
                VectorizeRequest.builder()
                    .text(request.getQuery())
                    .modelName("bge-large-zh")
                    .build()
            );

            if (!vectorResult.isSuccess()) {
                return Collections.emptyList();
            }

            // 2. 向量检索
            SearchRequest searchRequest = SearchRequest.builder()
                .queryVector(vectorResult.getVector())
                .topK(request.getTopK())
                .threshold(request.getThreshold())
                .filters(request.getFilters())
                .build();

            List<SearchResult> searchResults = milvusService.search(searchRequest);

            // 3. 转换为统一结果格式
            return searchResults.stream()
                .map(this::convertToRetrievalResult)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("文档数据源检索失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public boolean isAvailable() {
        try {
            return milvusService.healthCheck();
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public double getReliabilityScore() {
        return 0.9; // 文档数据源可靠性较高
    }

    private RetrievalResult convertToRetrievalResult(SearchResult searchResult) {
        return RetrievalResult.builder()
            .id(searchResult.getId())
            .content(searchResult.getContent())
            .score(searchResult.getScore())
            .sourceType("DOCUMENT")
            .metadata(searchResult.getMetadata())
            .build();
    }
}

@Component
@Slf4j
public class QADataSource implements DataSource {

    @Autowired
    private QAPairService qaPairService;

    @Override
    public String getSourceType() {
        return "QA_PAIR";
    }

    @Override
    public List<RetrievalResult> retrieve(RetrievalRequest request) {
        try {
            List<QAPair> qaPairs = qaPairService.searchSimilarQA(
                request.getQuery(),
                request.getKnowledgeBaseId(),
                request.getTopK()
            );

            return qaPairs.stream()
                .map(this::convertToRetrievalResult)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("QA数据源检索失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public boolean isAvailable() {
        return true; // QA数据源通常可用
    }

    @Override
    public double getReliabilityScore() {
        return 0.95; // QA对可靠性很高
    }

    private RetrievalResult convertToRetrievalResult(QAPair qaPair) {
        return RetrievalResult.builder()
            .id(qaPair.getId())
            .content(qaPair.getAnswer())
            .score(qaPair.getConfidenceScore())
            .sourceType("QA_PAIR")
            .metadata(Map.of(
                "question", qaPair.getQuestion(),
                "answer_type", qaPair.getAnswerType(),
                "usage_count", qaPair.getUsageCount()
            ))
            .build();
    }
}

@Component
@Slf4j
public class StructuredDataSource implements DataSource {

    @Autowired
    private Text2SQLService text2SQLService;

    @Override
    public String getSourceType() {
        return "STRUCTURED_DATA";
    }

    @Override
    public List<RetrievalResult> retrieve(RetrievalRequest request) {
        try {
            // 1. 自然语言转SQL
            SQLGenerationResult sqlResult = text2SQLService.generateSQL(request.getQuery());

            if (!sqlResult.isSuccess()) {
                return Collections.emptyList();
            }

            // 2. 执行SQL查询
            QueryResult queryResult = text2SQLService.executeQuery(sqlResult.getSql());

            if (!queryResult.isSuccess()) {
                return Collections.emptyList();
            }

            // 3. 格式化结果
            String formattedResult = text2SQLService.formatResult(queryResult);

            return Collections.singletonList(
                RetrievalResult.builder()
                    .id("sql_" + System.currentTimeMillis())
                    .content(formattedResult)
                    .score(sqlResult.getConfidence())
                    .sourceType("STRUCTURED_DATA")
                    .metadata(Map.of(
                        "sql", sqlResult.getSql(),
                        "table_count", queryResult.getTableCount(),
                        "row_count", queryResult.getRowCount()
                    ))
                    .build()
            );

        } catch (Exception e) {
            log.error("结构化数据源检索失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public boolean isAvailable() {
        return text2SQLService.isAvailable();
    }

    @Override
    public double getReliabilityScore() {
        return 0.8; // 结构化数据准确但覆盖面有限
    }
}
```

### 8.2 召回策略实现

```java
@Service
@Slf4j
public class MultiSourceRetrievalService {

    @Autowired
    private List<DataSource> dataSources;

    @Autowired
    private RetrievalStrategySelector strategySelector;

    @Autowired
    private ResultFusionService fusionService;

    public MultiSourceRetrievalResult retrieve(RetrievalRequest request) {
        try {
            // 1. 选择召回策略
            RetrievalStrategy strategy = strategySelector.selectStrategy(request);

            // 2. 根据策略执行召回
            List<SourceResult> sourceResults = executeRetrieval(request, strategy);

            // 3. 融合多源结果
            List<RetrievalResult> fusedResults = fusionService.fuseResults(sourceResults, request);

            return MultiSourceRetrievalResult.builder()
                .results(fusedResults)
                .sourceResults(sourceResults)
                .strategy(strategy)
                .totalSources(sourceResults.size())
                .build();

        } catch (Exception e) {
            log.error("多源召回失败", e);
            throw new RetrievalException("多源召回失败", e);
        }
    }

    private List<SourceResult> executeRetrieval(RetrievalRequest request, RetrievalStrategy strategy) {
        switch (strategy.getType()) {
            case PARALLEL:
                return executeParallelRetrieval(request, strategy);
            case SEQUENTIAL:
                return executeSequentialRetrieval(request, strategy);
            case CONDITIONAL:
                return executeConditionalRetrieval(request, strategy);
            default:
                throw new UnsupportedOperationException("不支持的召回策略: " + strategy.getType());
        }
    }

    private List<SourceResult> executeParallelRetrieval(RetrievalRequest request, RetrievalStrategy strategy) {
        List<DataSource> selectedSources = selectDataSources(strategy.getSourceTypes());

        // 并行执行检索
        List<CompletableFuture<SourceResult>> futures = selectedSources.stream()
            .map(source -> CompletableFuture.supplyAsync(() -> {
                try {
                    long startTime = System.currentTimeMillis();
                    List<RetrievalResult> results = source.retrieve(request);
                    long duration = System.currentTimeMillis() - startTime;

                    return SourceResult.builder()
                        .sourceType(source.getSourceType())
                        .results(results)
                        .duration(duration)
                        .success(true)
                        .build();

                } catch (Exception e) {
                    log.error("数据源检索失败: {}", source.getSourceType(), e);
                    return SourceResult.builder()
                        .sourceType(source.getSourceType())
                        .results(Collections.emptyList())
                        .success(false)
                        .errorMessage(e.getMessage())
                        .build();
                }
            }))
            .collect(Collectors.toList());

        // 等待所有任务完成
        return futures.stream()
            .map(CompletableFuture::join)
            .collect(Collectors.toList());
    }

    private List<SourceResult> executeSequentialRetrieval(RetrievalRequest request, RetrievalStrategy strategy) {
        List<SourceResult> results = new ArrayList<>();
        List<DataSource> selectedSources = selectDataSources(strategy.getSourceTypes());

        for (DataSource source : selectedSources) {
            try {
                long startTime = System.currentTimeMillis();
                List<RetrievalResult> sourceResults = source.retrieve(request);
                long duration = System.currentTimeMillis() - startTime;

                SourceResult result = SourceResult.builder()
                    .sourceType(source.getSourceType())
                    .results(sourceResults)
                    .duration(duration)
                    .success(true)
                    .build();

                results.add(result);

                // 检查是否满足早停条件
                if (shouldStopEarly(results, strategy)) {
                    break;
                }

            } catch (Exception e) {
                log.error("数据源检索失败: {}", source.getSourceType(), e);
                results.add(SourceResult.builder()
                    .sourceType(source.getSourceType())
                    .results(Collections.emptyList())
                    .success(false)
                    .errorMessage(e.getMessage())
                    .build());
            }
        }

        return results;
    }

    private List<SourceResult> executeConditionalRetrieval(RetrievalRequest request, RetrievalStrategy strategy) {
        List<SourceResult> results = new ArrayList<>();

        // 首先尝试高优先级数据源
        List<DataSource> highPrioritySources = selectDataSources(strategy.getHighPrioritySources());

        for (DataSource source : highPrioritySources) {
            SourceResult result = retrieveFromSource(source, request);
            results.add(result);

            // 如果高优先级源返回了足够的结果，则不再查询其他源
            if (result.isSuccess() && result.getResults().size() >= strategy.getMinResultsThreshold()) {
                return results;
            }
        }

        // 如果高优先级源结果不足，则查询低优先级源
        List<DataSource> lowPrioritySources = selectDataSources(strategy.getLowPrioritySources());

        for (DataSource source : lowPrioritySources) {
            SourceResult result = retrieveFromSource(source, request);
            results.add(result);

            // 检查总结果数是否满足要求
            int totalResults = results.stream()
                .mapToInt(r -> r.getResults().size())
                .sum();

            if (totalResults >= request.getTopK()) {
                break;
            }
        }

        return results;
    }

    private SourceResult retrieveFromSource(DataSource source, RetrievalRequest request) {
        try {
            long startTime = System.currentTimeMillis();
            List<RetrievalResult> results = source.retrieve(request);
            long duration = System.currentTimeMillis() - startTime;

            return SourceResult.builder()
                .sourceType(source.getSourceType())
                .results(results)
                .duration(duration)
                .success(true)
                .build();

        } catch (Exception e) {
            log.error("数据源检索失败: {}", source.getSourceType(), e);
            return SourceResult.builder()
                .sourceType(source.getSourceType())
                .results(Collections.emptyList())
                .success(false)
                .errorMessage(e.getMessage())
                .build();
        }
    }

    private List<DataSource> selectDataSources(List<String> sourceTypes) {
        return dataSources.stream()
            .filter(source -> sourceTypes.contains(source.getSourceType()))
            .filter(DataSource::isAvailable)
            .collect(Collectors.toList());
    }

    private boolean shouldStopEarly(List<SourceResult> results, RetrievalStrategy strategy) {
        int totalResults = results.stream()
            .mapToInt(r -> r.getResults().size())
            .sum();

        return totalResults >= strategy.getEarlyStopThreshold();
    }
}
```

## 9. 问题优化扩充

### 9.1 查询扩展服务

```java
@Service
@Slf4j
public class QueryExpansionService {

    @Autowired
    private LLMModelRouter llmRouter;

    @Autowired
    private SynonymService synonymService;

    @Autowired
    private QueryExpansionCache expansionCache;

    public QueryExpansionResult expandQuery(String originalQuery, ExpansionConfig config) {
        try {
            // 1. 检查缓存
            String cacheKey = generateExpansionCacheKey(originalQuery, config);
            QueryExpansionResult cachedResult = expansionCache.getCachedExpansion(cacheKey);
            if (cachedResult != null) {
                return cachedResult;
            }

            List<String> expandedQueries = new ArrayList<>();
            expandedQueries.add(originalQuery); // 保留原始查询

            // 2. 同义词扩展
            if (config.isEnableSynonymExpansion()) {
                List<String> synonymQueries = generateSynonymQueries(originalQuery, config);
                expandedQueries.addAll(synonymQueries);
            }

            // 3. 相关词扩展
            if (config.isEnableRelatedTermExpansion()) {
                List<String> relatedQueries = generateRelatedQueries(originalQuery, config);
                expandedQueries.addAll(relatedQueries);
            }

            // 4. 上下文扩展
            if (config.isEnableContextExpansion()) {
                List<String> contextQueries = generateContextQueries(originalQuery, config);
                expandedQueries.addAll(contextQueries);
            }

            // 5. 去重和排序
            List<String> finalQueries = deduplicateAndRank(expandedQueries, originalQuery);

            QueryExpansionResult result = QueryExpansionResult.builder()
                .originalQuery(originalQuery)
                .expandedQueries(finalQueries)
                .expansionMethods(getUsedMethods(config))
                .confidence(calculateExpansionConfidence(finalQueries))
                .build();

            // 6. 缓存结果
            expansionCache.cacheExpansion(cacheKey, result);

            return result;

        } catch (Exception e) {
            log.error("查询扩展失败: {}", originalQuery, e);
            return QueryExpansionResult.builder()
                .originalQuery(originalQuery)
                .expandedQueries(Collections.singletonList(originalQuery))
                .build();
        }
    }

    private List<String> generateSynonymQueries(String query, ExpansionConfig config) {
        List<String> synonymQueries = new ArrayList<>();

        // 使用同义词词典
        List<String> synonyms = synonymService.findSynonyms(query);
        for (String synonym : synonyms) {
            if (!synonym.equals(query)) {
                synonymQueries.add(synonym);
            }
        }

        return synonymQueries.stream()
            .limit(config.getMaxSynonymExpansions())
            .collect(Collectors.toList());
    }

    private List<String> generateRelatedQueries(String query, ExpansionConfig config) {
        try {
            String prompt = buildRelatedTermPrompt(query, config);

            LLMRequest request = LLMRequest.builder()
                .prompt(prompt)
                .maxTokens(config.getMaxTokens())
                .temperature(0.3)
                .build();

            LLMModel model = llmRouter.selectModel(config.getModelName());
            LLMResponse response = model.generate(request);

            if (response.isSuccess()) {
                return parseRelatedQueries(response.getContent());
            }

        } catch (Exception e) {
            log.warn("相关词扩展失败: {}", query, e);
        }

        return Collections.emptyList();
    }

    private List<String> generateContextQueries(String query, ExpansionConfig config) {
        try {
            String prompt = buildContextExpansionPrompt(query, config);

            LLMRequest request = LLMRequest.builder()
                .prompt(prompt)
                .maxTokens(config.getMaxTokens())
                .temperature(0.4)
                .build();

            LLMModel model = llmRouter.selectModel(config.getModelName());
            LLMResponse response = model.generate(request);

            if (response.isSuccess()) {
                return parseContextQueries(response.getContent());
            }

        } catch (Exception e) {
            log.warn("上下文扩展失败: {}", query, e);
        }

        return Collections.emptyList();
    }

    private String buildRelatedTermPrompt(String query, ExpansionConfig config) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("你是一个专业的查询扩展专家。请为以下查询生成相关的搜索词。\n\n");
        prompt.append("要求：\n");
        prompt.append("- 生成").append(config.getMaxRelatedExpansions()).append("个相关搜索词\n");
        prompt.append("- 保持语义相关性\n");
        prompt.append("- 涵盖不同角度和层面\n");
        prompt.append("- 每行一个搜索词\n\n");

        prompt.append("原始查询：").append(query).append("\n\n");
        prompt.append("相关搜索词：");

        return prompt.toString();
    }

    private String buildContextExpansionPrompt(String query, ExpansionConfig config) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("你是一个专业的查询扩展专家。请为以下查询生成上下文相关的搜索变体。\n\n");
        prompt.append("要求：\n");
        prompt.append("- 生成").append(config.getMaxContextExpansions()).append("个上下文变体\n");
        prompt.append("- 从不同角度表达相同意图\n");
        prompt.append("- 包含更具体或更抽象的表述\n");
        prompt.append("- 每行一个查询变体\n\n");

        prompt.append("原始查询：").append(query).append("\n\n");
        prompt.append("上下文变体：");

        return prompt.toString();
    }

    private List<String> parseRelatedQueries(String response) {
        return Arrays.stream(response.split("\n"))
            .map(String::trim)
            .filter(line -> !line.isEmpty())
            .filter(line -> !line.startsWith("相关搜索词"))
            .collect(Collectors.toList());
    }

    private List<String> parseContextQueries(String response) {
        return Arrays.stream(response.split("\n"))
            .map(String::trim)
            .filter(line -> !line.isEmpty())
            .filter(line -> !line.startsWith("上下文变体"))
            .collect(Collectors.toList());
    }

    private List<String> deduplicateAndRank(List<String> queries, String originalQuery) {
        return queries.stream()
            .distinct()
            .sorted((q1, q2) -> {
                // 原始查询优先级最高
                if (q1.equals(originalQuery)) return -1;
                if (q2.equals(originalQuery)) return 1;

                // 按长度和相似度排序
                double sim1 = calculateSimilarity(q1, originalQuery);
                double sim2 = calculateSimilarity(q2, originalQuery);
                return Double.compare(sim2, sim1);
            })
            .collect(Collectors.toList());
    }

    private double calculateSimilarity(String query1, String query2) {
        // 简单的Jaccard相似度计算
        Set<String> set1 = new HashSet<>(Arrays.asList(query1.split("\\s+")));
        Set<String> set2 = new HashSet<>(Arrays.asList(query2.split("\\s+")));

        Set<String> intersection = new HashSet<>(set1);
        intersection.retainAll(set2);

        Set<String> union = new HashSet<>(set1);
        union.addAll(set2);

        return union.isEmpty() ? 0.0 : (double) intersection.size() / union.size();
    }
}
```

### 9.2 问题重写服务

```java
@Service
@Slf4j
public class QueryRewriteService {

    @Autowired
    private LLMModelRouter llmRouter;

    @Autowired
    private QueryRewriteCache rewriteCache;

    public QueryRewriteResult rewriteQuery(String originalQuery, RewriteConfig config) {
        try {
            // 1. 检查缓存
            String cacheKey = generateRewriteCacheKey(originalQuery, config);
            QueryRewriteResult cachedResult = rewriteCache.getCachedRewrite(cacheKey);
            if (cachedResult != null) {
                return cachedResult;
            }

            // 2. 构建重写提示词
            String prompt = buildRewritePrompt(originalQuery, config);

            // 3. 调用LLM进行重写
            LLMRequest request = LLMRequest.builder()
                .prompt(prompt)
                .maxTokens(config.getMaxTokens())
                .temperature(0.2)
                .build();

            LLMModel model = llmRouter.selectModel(config.getModelName());
            LLMResponse response = model.generate(request);

            if (response.isSuccess()) {
                List<String> rewrittenQueries = parseRewrittenQueries(response.getContent());

                QueryRewriteResult result = QueryRewriteResult.builder()
                    .originalQuery(originalQuery)
                    .rewrittenQueries(rewrittenQueries)
                    .confidence(response.getConfidence())
                    .build();

                // 4. 缓存结果
                rewriteCache.cacheRewrite(cacheKey, result);

                return result;
            } else {
                log.warn("查询重写失败: {}", response.getErrorMessage());
                return QueryRewriteResult.builder()
                    .originalQuery(originalQuery)
                    .rewrittenQueries(Collections.singletonList(originalQuery))
                    .build();
            }

        } catch (Exception e) {
            log.error("查询重写异常: {}", originalQuery, e);
            return QueryRewriteResult.builder()
                .originalQuery(originalQuery)
                .rewrittenQueries(Collections.singletonList(originalQuery))
                .build();
        }
    }

    private String buildRewritePrompt(String query, RewriteConfig config) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("你是一个专业的查询优化专家。请将以下查询重写为更适合检索的形式。\n\n");

        prompt.append("重写目标：\n");
        prompt.append("- 提高检索准确性\n");
        prompt.append("- 使用更精确的术语\n");
        prompt.append("- 消除歧义表达\n");
        prompt.append("- 保持原始意图\n\n");

        prompt.append("重写策略：\n");
        switch (config.getRewriteStrategy()) {
            case CLARIFICATION:
                prompt.append("- 澄清模糊概念\n");
                prompt.append("- 添加必要的限定词\n");
                break;
            case SIMPLIFICATION:
                prompt.append("- 简化复杂表述\n");
                prompt.append("- 使用通俗易懂的词汇\n");
                break;
            case EXPANSION:
                prompt.append("- 扩展关键信息\n");
                prompt.append("- 添加相关背景\n");
                break;
            case NORMALIZATION:
                prompt.append("- 标准化术语表达\n");
                prompt.append("- 统一格式规范\n");
                break;
        }

        prompt.append("\n生成").append(config.getMaxRewrites()).append("个重写版本，每行一个：\n\n");

        prompt.append("原始查询：").append(query).append("\n\n");
        prompt.append("重写版本：");

        return prompt.toString();
    }

    private List<String> parseRewrittenQueries(String response) {
        return Arrays.stream(response.split("\n"))
            .map(String::trim)
            .filter(line -> !line.isEmpty())
            .filter(line -> !line.startsWith("重写版本"))
            .filter(line -> !line.matches("^\\d+[.、].*")) // 移除序号
            .map(line -> line.replaceAll("^[•\\-*]\\s*", "")) // 移除项目符号
            .collect(Collectors.toList());
    }

    private String generateRewriteCacheKey(String query, RewriteConfig config) {
        String content = query + ":" + config.getRewriteStrategy() + ":" + config.getMaxRewrites();
        return DigestUtils.sha256Hex(content);
    }
}
```

## 10. CoT思维链+反思机制

### 10.1 CoT提示词模板

```java
@Component
public class CoTPromptBuilder {

    public String buildCoTPrompt(String question, List<RetrievalResult> context, CoTConfig config) {
        StringBuilder prompt = new StringBuilder();

        // 1. 系统角色设定
        prompt.append("你是一个专业的知识分析专家。请使用step-by-step的思维方式来回答问题。\n\n");

        // 2. 思维链指导
        prompt.append("思维链步骤：\n");
        prompt.append("1. 问题理解：分析问题的核心意图和关键要素\n");
        prompt.append("2. 信息梳理：整理和分析提供的参考信息\n");
        prompt.append("3. 逻辑推理：基于信息进行逐步推理\n");
        prompt.append("4. 答案构建：形成完整、准确的答案\n");
        prompt.append("5. 质量检查：检查答案的逻辑性和完整性\n\n");

        // 3. 参考信息
        if (!context.isEmpty()) {
            prompt.append("参考信息：\n");
            for (int i = 0; i < context.size(); i++) {
                RetrievalResult result = context.get(i);
                prompt.append("【信息").append(i + 1).append("】").append(result.getContent()).append("\n\n");
            }
        }

        // 4. 问题
        prompt.append("问题：").append(question).append("\n\n");

        // 5. 输出格式要求
        prompt.append("请按照以下格式回答：\n\n");
        prompt.append("## 思维过程\n");
        prompt.append("### 1. 问题理解\n");
        prompt.append("[分析问题的核心意图]\n\n");
        prompt.append("### 2. 信息梳理\n");
        prompt.append("[整理关键信息点]\n\n");
        prompt.append("### 3. 逻辑推理\n");
        prompt.append("[逐步推理过程]\n\n");
        prompt.append("### 4. 答案构建\n");
        prompt.append("[形成最终答案]\n\n");
        prompt.append("### 5. 质量检查\n");
        prompt.append("[检查答案质量]\n\n");
        prompt.append("## 最终答案\n");
        prompt.append("[提供简洁明确的最终答案]\n");

        return prompt.toString();
    }

    public String buildReflectionPrompt(String question, String answer, ReflectionConfig config) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("你是一个专业的答案质量评估专家。请对以下问答进行反思和评估。\n\n");

        prompt.append("评估维度：\n");
        prompt.append("1. 准确性：答案是否准确无误\n");
        prompt.append("2. 完整性：答案是否完整回答了问题\n");
        prompt.append("3. 相关性：答案是否与问题高度相关\n");
        prompt.append("4. 逻辑性：答案的逻辑是否清晰合理\n");
        prompt.append("5. 可理解性：答案是否易于理解\n\n");

        prompt.append("问题：").append(question).append("\n\n");
        prompt.append("答案：").append(answer).append("\n\n");

        prompt.append("请按照以下格式进行评估：\n\n");
        prompt.append("## 质量评估\n");
        prompt.append("### 准确性评分：[1-10分]\n");
        prompt.append("### 完整性评分：[1-10分]\n");
        prompt.append("### 相关性评分：[1-10分]\n");
        prompt.append("### 逻辑性评分：[1-10分]\n");
        prompt.append("### 可理解性评分：[1-10分]\n\n");
        prompt.append("## 问题识别\n");
        prompt.append("[指出答案中存在的问题]\n\n");
        prompt.append("## 改进建议\n");
        prompt.append("[提供具体的改进建议]\n\n");
        prompt.append("## 总体评价\n");
        prompt.append("综合评分：[1-10分]\n");
        prompt.append("是否需要重新回答：[是/否]\n");

        return prompt.toString();
    }
}
```

## 11. 混合检索优化

### 11.1 检索策略组合

```java
@Service
@Slf4j
public class HybridRetrievalService {

    @Autowired
    private VectorRetrievalService vectorRetrieval;

    @Autowired
    private KeywordRetrievalService keywordRetrieval;

    @Autowired
    private SemanticRetrievalService semanticRetrieval;

    @Autowired
    private RetrievalWeightOptimizer weightOptimizer;

    public HybridRetrievalResult hybridSearch(HybridSearchRequest request) {
        try {
            // 1. 动态调整检索权重
            RetrievalWeights weights = weightOptimizer.optimizeWeights(request);

            // 2. 并行执行多种检索
            CompletableFuture<List<RetrievalResult>> vectorFuture = CompletableFuture.supplyAsync(() ->
                vectorRetrieval.search(request.toVectorRequest()));

            CompletableFuture<List<RetrievalResult>> keywordFuture = CompletableFuture.supplyAsync(() ->
                keywordRetrieval.search(request.toKeywordRequest()));

            CompletableFuture<List<RetrievalResult>> semanticFuture = CompletableFuture.supplyAsync(() ->
                semanticRetrieval.search(request.toSemanticRequest()));

            // 3. 等待所有检索完成
            List<RetrievalResult> vectorResults = vectorFuture.join();
            List<RetrievalResult> keywordResults = keywordFuture.join();
            List<RetrievalResult> semanticResults = semanticFuture.join();

            // 4. 融合检索结果
            List<RetrievalResult> fusedResults = fuseResults(
                vectorResults, keywordResults, semanticResults, weights);

            // 5. 智能去重
            List<RetrievalResult> deduplicatedResults = intelligentDeduplication(fusedResults);

            // 6. 最终排序和截取
            List<RetrievalResult> finalResults = deduplicatedResults.stream()
                .sorted((r1, r2) -> Double.compare(r2.getScore(), r1.getScore()))
                .limit(request.getTopK())
                .collect(Collectors.toList());

            return HybridRetrievalResult.builder()
                .results(finalResults)
                .vectorResults(vectorResults)
                .keywordResults(keywordResults)
                .semanticResults(semanticResults)
                .weights(weights)
                .totalCandidates(vectorResults.size() + keywordResults.size() + semanticResults.size())
                .build();

        } catch (Exception e) {
            log.error("混合检索失败", e);
            throw new RetrievalException("混合检索失败", e);
        }
    }

    private List<RetrievalResult> fuseResults(
            List<RetrievalResult> vectorResults,
            List<RetrievalResult> keywordResults,
            List<RetrievalResult> semanticResults,
            RetrievalWeights weights) {

        Map<String, RetrievalResult> resultMap = new HashMap<>();

        // 处理向量检索结果
        for (RetrievalResult result : vectorResults) {
            String key = result.getId();
            RetrievalResult fusedResult = resultMap.getOrDefault(key, result.copy());
            fusedResult.setScore(fusedResult.getScore() * weights.getVectorWeight());
            fusedResult.addSource("VECTOR");
            resultMap.put(key, fusedResult);
        }

        // 处理关键词检索结果
        for (RetrievalResult result : keywordResults) {
            String key = result.getId();
            RetrievalResult fusedResult = resultMap.get(key);

            if (fusedResult != null) {
                // 多源融合，提升分数
                double combinedScore = fusedResult.getScore() + result.getScore() * weights.getKeywordWeight();
                fusedResult.setScore(combinedScore);
                fusedResult.addSource("KEYWORD");
            } else {
                // 新结果
                result.setScore(result.getScore() * weights.getKeywordWeight());
                result.addSource("KEYWORD");
                resultMap.put(key, result);
            }
        }

        // 处理语义检索结果
        for (RetrievalResult result : semanticResults) {
            String key = result.getId();
            RetrievalResult fusedResult = resultMap.get(key);

            if (fusedResult != null) {
                double combinedScore = fusedResult.getScore() + result.getScore() * weights.getSemanticWeight();
                fusedResult.setScore(combinedScore);
                fusedResult.addSource("SEMANTIC");
            } else {
                result.setScore(result.getScore() * weights.getSemanticWeight());
                result.addSource("SEMANTIC");
                resultMap.put(key, result);
            }
        }

        return new ArrayList<>(resultMap.values());
    }

    private List<RetrievalResult> intelligentDeduplication(List<RetrievalResult> results) {
        List<RetrievalResult> deduplicatedResults = new ArrayList<>();
        Set<String> seenContent = new HashSet<>();

        for (RetrievalResult result : results) {
            String contentHash = DigestUtils.sha256Hex(result.getContent());

            if (!seenContent.contains(contentHash)) {
                seenContent.add(contentHash);
                deduplicatedResults.add(result);
            } else {
                // 内容重复，但可能来自不同源，合并信息
                Optional<RetrievalResult> existing = deduplicatedResults.stream()
                    .filter(r -> DigestUtils.sha256Hex(r.getContent()).equals(contentHash))
                    .findFirst();

                if (existing.isPresent()) {
                    RetrievalResult existingResult = existing.get();
                    // 取更高的分数
                    if (result.getScore() > existingResult.getScore()) {
                        existingResult.setScore(result.getScore());
                    }
                    // 合并来源信息
                    existingResult.getSources().addAll(result.getSources());
                }
            }
        }

        return deduplicatedResults;
    }
}

@Component
@Slf4j
public class RetrievalWeightOptimizer {

    @Autowired
    private QueryAnalyzer queryAnalyzer;

    @Autowired
    private PerformanceMetrics performanceMetrics;

    public RetrievalWeights optimizeWeights(HybridSearchRequest request) {
        try {
            // 1. 分析查询特征
            QueryFeatures features = queryAnalyzer.analyzeQuery(request.getQuery());

            // 2. 基于查询特征调整权重
            RetrievalWeights baseWeights = getBaseWeights(features);

            // 3. 基于历史性能调整
            RetrievalWeights adjustedWeights = adjustByPerformance(baseWeights, features);

            // 4. 确保权重和为1
            return normalizeWeights(adjustedWeights);

        } catch (Exception e) {
            log.warn("权重优化失败，使用默认权重", e);
            return RetrievalWeights.getDefault();
        }
    }

    private RetrievalWeights getBaseWeights(QueryFeatures features) {
        RetrievalWeights.Builder builder = RetrievalWeights.builder();

        // 根据查询类型调整权重
        switch (features.getQueryType()) {
            case FACTUAL:
                // 事实性查询，向量检索效果好
                builder.vectorWeight(0.6).keywordWeight(0.3).semanticWeight(0.1);
                break;
            case CONCEPTUAL:
                // 概念性查询，语义检索效果好
                builder.vectorWeight(0.4).keywordWeight(0.2).semanticWeight(0.4);
                break;
            case SPECIFIC:
                // 具体查询，关键词检索效果好
                builder.vectorWeight(0.3).keywordWeight(0.5).semanticWeight(0.2);
                break;
            default:
                builder.vectorWeight(0.5).keywordWeight(0.3).semanticWeight(0.2);
        }

        // 根据查询长度调整
        if (features.getQueryLength() < 5) {
            // 短查询，增加关键词权重
            builder.keywordWeight(builder.build().getKeywordWeight() + 0.1);
        } else if (features.getQueryLength() > 20) {
            // 长查询，增加语义权重
            builder.semanticWeight(builder.build().getSemanticWeight() + 0.1);
        }

        return builder.build();
    }

    private RetrievalWeights adjustByPerformance(RetrievalWeights baseWeights, QueryFeatures features) {
        // 获取历史性能数据
        PerformanceData perfData = performanceMetrics.getPerformanceData(features);

        if (perfData == null || !perfData.hasEnoughData()) {
            return baseWeights;
        }

        // 基于性能调整权重
        double vectorPerf = perfData.getVectorPerformance();
        double keywordPerf = perfData.getKeywordPerformance();
        double semanticPerf = perfData.getSemanticPerformance();

        double totalPerf = vectorPerf + keywordPerf + semanticPerf;

        if (totalPerf > 0) {
            return RetrievalWeights.builder()
                .vectorWeight(baseWeights.getVectorWeight() * (1 + vectorPerf / totalPerf))
                .keywordWeight(baseWeights.getKeywordWeight() * (1 + keywordPerf / totalPerf))
                .semanticWeight(baseWeights.getSemanticWeight() * (1 + semanticPerf / totalPerf))
                .build();
        }

        return baseWeights;
    }

    private RetrievalWeights normalizeWeights(RetrievalWeights weights) {
        double total = weights.getVectorWeight() + weights.getKeywordWeight() + weights.getSemanticWeight();

        if (total <= 0) {
            return RetrievalWeights.getDefault();
        }

        return RetrievalWeights.builder()
            .vectorWeight(weights.getVectorWeight() / total)
            .keywordWeight(weights.getKeywordWeight() / total)
            .semanticWeight(weights.getSemanticWeight() / total)
            .build();
    }
}
```

## 12. Text2SQL结构化数据检索

### 12.1 SQL生成服务

```java
@Service
@Slf4j
public class Text2SQLService {

    @Autowired
    private LLMModelRouter llmRouter;

    @Autowired
    private DatabaseSchemaService schemaService;

    @Autowired
    private SQLExecutor sqlExecutor;

    @Autowired
    private SQLValidationService validationService;

    public SQLGenerationResult generateSQL(String naturalLanguageQuery) {
        try {
            // 1. 获取数据库schema
            DatabaseSchema schema = schemaService.getSchema();

            // 2. 构建SQL生成提示词
            String prompt = buildSQLPrompt(naturalLanguageQuery, schema);

            // 3. 调用LLM生成SQL
            LLMRequest request = LLMRequest.builder()
                .prompt(prompt)
                .maxTokens(800)
                .temperature(0.1) // 低温度确保SQL准确性
                .build();

            LLMModel model = llmRouter.selectModel("gpt-4");
            LLMResponse response = model.generate(request);

            if (response.isSuccess()) {
                String generatedSQL = extractSQLFromResponse(response.getContent());

                // 4. 验证SQL语法
                SQLValidationResult validation = validationService.validateSQL(generatedSQL);

                if (validation.isValid()) {
                    return SQLGenerationResult.builder()
                        .sql(generatedSQL)
                        .confidence(response.getConfidence())
                        .explanation(extractExplanation(response.getContent()))
                        .success(true)
                        .build();
                } else {
                    log.warn("生成的SQL语法错误: {}", validation.getErrorMessage());
                    return SQLGenerationResult.failure("SQL语法错误: " + validation.getErrorMessage());
                }
            } else {
                return SQLGenerationResult.failure("SQL生成失败: " + response.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("Text2SQL转换失败: {}", naturalLanguageQuery, e);
            return SQLGenerationResult.failure("SQL生成异常: " + e.getMessage());
        }
    }

    private String buildSQLPrompt(String query, DatabaseSchema schema) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("你是一个专业的SQL生成专家。请将自然语言查询转换为准确的SQL语句。\n\n");

        // 数据库schema信息
        prompt.append("数据库Schema：\n");
        for (TableSchema table : schema.getTables()) {
            prompt.append("表名：").append(table.getTableName()).append("\n");
            prompt.append("字段：\n");
            for (ColumnSchema column : table.getColumns()) {
                prompt.append("  - ").append(column.getColumnName())
                      .append(" (").append(column.getDataType()).append(")")
                      .append(column.isNullable() ? " NULL" : " NOT NULL");
                if (column.isPrimaryKey()) {
                    prompt.append(" PRIMARY KEY");
                }
                prompt.append("\n");
            }
            prompt.append("\n");
        }

        // SQL生成规则
        prompt.append("SQL生成规则：\n");
        prompt.append("1. 只使用提供的表和字段\n");
        prompt.append("2. 使用标准SQL语法\n");
        prompt.append("3. 添加适当的WHERE条件\n");
        prompt.append("4. 考虑性能优化\n");
        prompt.append("5. 处理可能的NULL值\n\n");

        // 输出格式
        prompt.append("输出格式：\n");
        prompt.append("```sql\n");
        prompt.append("[生成的SQL语句]\n");
        prompt.append("```\n\n");
        prompt.append("解释：[简要说明SQL逻辑]\n\n");

        prompt.append("自然语言查询：").append(query).append("\n\n");
        prompt.append("请生成对应的SQL语句：");

        return prompt.toString();
    }

    private String extractSQLFromResponse(String response) {
        // 提取```sql```代码块中的SQL
        Pattern pattern = Pattern.compile("```sql\\s*\\n(.*?)\\n```", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(response);

        if (matcher.find()) {
            return matcher.group(1).trim();
        }

        // 如果没有代码块，尝试提取SELECT语句
        Pattern selectPattern = Pattern.compile("(SELECT.*?;?)", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
        Matcher selectMatcher = selectPattern.matcher(response);

        if (selectMatcher.find()) {
            return selectMatcher.group(1).trim();
        }

        return response.trim();
    }

    private String extractExplanation(String response) {
        String[] lines = response.split("\n");
        StringBuilder explanation = new StringBuilder();
        boolean inExplanation = false;

        for (String line : lines) {
            if (line.startsWith("解释：")) {
                inExplanation = true;
                explanation.append(line.substring(3).trim());
            } else if (inExplanation && !line.trim().isEmpty()) {
                explanation.append(" ").append(line.trim());
            }
        }

        return explanation.toString();
    }

    public QueryResult executeQuery(String sql) {
        try {
            return sqlExecutor.executeQuery(sql);
        } catch (Exception e) {
            log.error("SQL执行失败: {}", sql, e);
            return QueryResult.failure("SQL执行失败: " + e.getMessage());
        }
    }

    public String formatResult(QueryResult queryResult) {
        if (!queryResult.isSuccess()) {
            return "查询失败：" + queryResult.getErrorMessage();
        }

        List<Map<String, Object>> rows = queryResult.getRows();
        if (rows.isEmpty()) {
            return "查询结果为空。";
        }

        StringBuilder result = new StringBuilder();
        result.append("查询结果（共").append(rows.size()).append("条记录）：\n\n");

        // 格式化为表格
        if (rows.size() <= 10) {
            // 少量数据，显示详细表格
            result.append(formatAsTable(rows));
        } else {
            // 大量数据，显示摘要
            result.append(formatAsSummary(rows));
        }

        return result.toString();
    }

    private String formatAsTable(List<Map<String, Object>> rows) {
        if (rows.isEmpty()) return "";

        StringBuilder table = new StringBuilder();
        Set<String> columns = rows.get(0).keySet();

        // 表头
        table.append("| ");
        for (String column : columns) {
            table.append(column).append(" | ");
        }
        table.append("\n");

        // 分隔线
        table.append("| ");
        for (String column : columns) {
            table.append("--- | ");
        }
        table.append("\n");

        // 数据行
        for (Map<String, Object> row : rows) {
            table.append("| ");
            for (String column : columns) {
                Object value = row.get(column);
                table.append(value != null ? value.toString() : "NULL").append(" | ");
            }
            table.append("\n");
        }

        return table.toString();
    }

    private String formatAsSummary(List<Map<String, Object>> rows) {
        StringBuilder summary = new StringBuilder();

        summary.append("数据摘要：\n");
        summary.append("- 总记录数：").append(rows.size()).append("\n");

        // 显示前5条记录
        summary.append("- 前5条记录：\n");
        for (int i = 0; i < Math.min(5, rows.size()); i++) {
            Map<String, Object> row = rows.get(i);
            summary.append("  ").append(i + 1).append(". ");

            // 只显示前3个字段
            int fieldCount = 0;
            for (Map.Entry<String, Object> entry : row.entrySet()) {
                if (fieldCount >= 3) break;
                if (fieldCount > 0) summary.append(", ");
                summary.append(entry.getKey()).append(": ").append(entry.getValue());
                fieldCount++;
            }
            summary.append("\n");
        }

        return summary.toString();
    }

    public boolean isAvailable() {
        try {
            return sqlExecutor.testConnection();
        } catch (Exception e) {
            return false;
        }
    }
}
```

## 13. 智能路由检索

### 13.1 路由决策引擎

```java
@Service
@Slf4j
public class IntelligentRoutingService {

    @Autowired
    private QueryAnalyzer queryAnalyzer;

    @Autowired
    private DataSourceAnalyzer dataSourceAnalyzer;

    @Autowired
    private RoutingRuleEngine ruleEngine;

    @Autowired
    private MLRoutingModel mlModel;

    @Autowired
    private PerformanceMonitor performanceMonitor;

    public RoutingDecision makeRoutingDecision(RoutingRequest request) {
        try {
            // 1. 分析查询特征
            QueryFeatures queryFeatures = queryAnalyzer.analyzeQuery(request.getQuery());

            // 2. 分析数据源特征
            DataSourceFeatures dataSourceFeatures = dataSourceAnalyzer.analyzeDataSources(
                request.getAvailableDataSources());

            // 3. 规则引擎决策
            RoutingDecision ruleDecision = ruleEngine.makeDecision(queryFeatures, dataSourceFeatures);

            // 4. 机器学习模型决策
            RoutingDecision mlDecision = mlModel.predict(queryFeatures, dataSourceFeatures);

            // 5. 融合决策结果
            RoutingDecision finalDecision = fuseDecisions(ruleDecision, mlDecision, request);

            // 6. 记录决策用于后续优化
            performanceMonitor.recordRoutingDecision(request, finalDecision);

            return finalDecision;

        } catch (Exception e) {
            log.error("路由决策失败", e);
            return getDefaultRoutingDecision(request);
        }
    }

    private RoutingDecision fuseDecisions(RoutingDecision ruleDecision, RoutingDecision mlDecision, RoutingRequest request) {
        // 基于置信度和历史性能融合决策
        double ruleConfidence = ruleDecision.getConfidence();
        double mlConfidence = mlDecision.getConfidence();

        // 获取历史性能数据
        PerformanceData rulePerf = performanceMonitor.getRulePerformance(request.getQueryType());
        PerformanceData mlPerf = performanceMonitor.getMLPerformance(request.getQueryType());

        // 计算融合权重
        double ruleWeight = ruleConfidence * (rulePerf != null ? rulePerf.getSuccessRate() : 0.7);
        double mlWeight = mlConfidence * (mlPerf != null ? mlPerf.getSuccessRate() : 0.6);

        if (ruleWeight > mlWeight) {
            return ruleDecision.toBuilder()
                .confidence((ruleWeight + mlWeight) / 2)
                .decisionSource("RULE_DOMINANT")
                .build();
        } else {
            return mlDecision.toBuilder()
                .confidence((ruleWeight + mlWeight) / 2)
                .decisionSource("ML_DOMINANT")
                .build();
        }
    }

    private RoutingDecision getDefaultRoutingDecision(RoutingRequest request) {
        return RoutingDecision.builder()
            .primaryDataSource("DOCUMENT")
            .secondaryDataSources(Arrays.asList("QA_PAIR"))
            .retrievalStrategy(RetrievalStrategyType.HYBRID)
            .confidence(0.5)
            .decisionSource("DEFAULT")
            .reasoning("使用默认路由策略")
            .build();
    }
}

@Component
@Slf4j
public class RoutingRuleEngine {

    public RoutingDecision makeDecision(QueryFeatures queryFeatures, DataSourceFeatures dataSourceFeatures) {
        RoutingDecision.Builder builder = RoutingDecision.builder();

        // 规则1：基于查询意图路由
        switch (queryFeatures.getIntentType()) {
            case FACTUAL:
                if (dataSourceFeatures.hasStructuredData()) {
                    builder.primaryDataSource("STRUCTURED_DATA")
                           .secondaryDataSources(Arrays.asList("DOCUMENT", "QA_PAIR"));
                } else {
                    builder.primaryDataSource("QA_PAIR")
                           .secondaryDataSources(Arrays.asList("DOCUMENT"));
                }
                break;

            case ANALYTICAL:
                builder.primaryDataSource("DOCUMENT")
                       .secondaryDataSources(Arrays.asList("QA_PAIR"));
                break;

            case PROCEDURAL:
                builder.primaryDataSource("DOCUMENT")
                       .secondaryDataSources(Arrays.asList("QA_PAIR"));
                break;

            default:
                builder.primaryDataSource("DOCUMENT")
                       .secondaryDataSources(Arrays.asList("QA_PAIR", "STRUCTURED_DATA"));
        }

        // 规则2：基于查询复杂度选择检索策略
        if (queryFeatures.getComplexity() == ComplexityLevel.SIMPLE) {
            builder.retrievalStrategy(RetrievalStrategyType.KEYWORD_FIRST);
        } else if (queryFeatures.getComplexity() == ComplexityLevel.COMPLEX) {
            builder.retrievalStrategy(RetrievalStrategyType.SEMANTIC_FIRST);
        } else {
            builder.retrievalStrategy(RetrievalStrategyType.HYBRID);
        }

        // 规则3：基于数据源可用性调整
        if (!dataSourceFeatures.isDataSourceAvailable(builder.build().getPrimaryDataSource())) {
            List<String> availableSources = dataSourceFeatures.getAvailableDataSources();
            if (!availableSources.isEmpty()) {
                builder.primaryDataSource(availableSources.get(0));
            }
        }

        // 计算规则置信度
        double confidence = calculateRuleConfidence(queryFeatures, dataSourceFeatures);

        return builder
            .confidence(confidence)
            .decisionSource("RULE_ENGINE")
            .reasoning(generateRuleReasoning(queryFeatures))
            .build();
    }

    private double calculateRuleConfidence(QueryFeatures queryFeatures, DataSourceFeatures dataSourceFeatures) {
        double confidence = 0.7; // 基础置信度

        // 基于查询清晰度调整
        if (queryFeatures.getClarityScore() > 0.8) {
            confidence += 0.1;
        } else if (queryFeatures.getClarityScore() < 0.5) {
            confidence -= 0.1;
        }

        // 基于数据源质量调整
        if (dataSourceFeatures.getAverageQuality() > 0.8) {
            confidence += 0.1;
        }

        return Math.max(0.1, Math.min(1.0, confidence));
    }

    private String generateRuleReasoning(QueryFeatures queryFeatures) {
        StringBuilder reasoning = new StringBuilder();
        reasoning.append("基于查询意图(").append(queryFeatures.getIntentType()).append(")");
        reasoning.append("和复杂度(").append(queryFeatures.getComplexity()).append(")");
        reasoning.append("选择路由策略");
        return reasoning.toString();
    }
}

@Component
@Slf4j
public class MLRoutingModel {

    @Autowired
    private ModelInferenceService inferenceService;

    @Autowired
    private FeatureExtractor featureExtractor;

    public RoutingDecision predict(QueryFeatures queryFeatures, DataSourceFeatures dataSourceFeatures) {
        try {
            // 1. 特征提取
            double[] features = featureExtractor.extractFeatures(queryFeatures, dataSourceFeatures);

            // 2. 模型推理
            ModelPrediction prediction = inferenceService.predict(features);

            // 3. 解析预测结果
            return parseModelPrediction(prediction);

        } catch (Exception e) {
            log.warn("ML模型预测失败，使用默认决策", e);
            return getDefaultMLDecision();
        }
    }

    private RoutingDecision parseModelPrediction(ModelPrediction prediction) {
        return RoutingDecision.builder()
            .primaryDataSource(prediction.getPrimaryDataSource())
            .secondaryDataSources(prediction.getSecondaryDataSources())
            .retrievalStrategy(prediction.getRetrievalStrategy())
            .confidence(prediction.getConfidence())
            .decisionSource("ML_MODEL")
            .reasoning("基于机器学习模型预测")
            .build();
    }

    private RoutingDecision getDefaultMLDecision() {
        return RoutingDecision.builder()
            .primaryDataSource("DOCUMENT")
            .secondaryDataSources(Arrays.asList("QA_PAIR"))
            .retrievalStrategy(RetrievalStrategyType.HYBRID)
            .confidence(0.5)
            .decisionSource("ML_DEFAULT")
            .reasoning("ML模型默认决策")
            .build();
    }
}
```

## 14. 重排序模块

### 14.1 重排模型选型对比

| 模型名称 | 类型 | 精度(NDCG@10) | 速度(QPS) | 内存占用 | 适用场景 | 推荐度 |
|----------|------|---------------|-----------|----------|----------|--------|
| **BGE-reranker-large** | Cross-encoder | 0.78 | 50 | 1.2GB | 高精度重排 | ⭐⭐⭐⭐⭐ |
| **BGE-reranker-base** | Cross-encoder | 0.75 | 120 | 400MB | 平衡性能 | ⭐⭐⭐⭐ |
| **ColBERT** | Late interaction | 0.72 | 200 | 800MB | 快速重排 | ⭐⭐⭐⭐ |
| **RankT5** | Generative | 0.76 | 30 | 1.5GB | 生成式重排 | ⭐⭐⭐ |
| **MonoT5** | Pointwise | 0.74 | 80 | 1.0GB | 单点重排 | ⭐⭐⭐ |

### 14.2 重排实现方案

```java
@Service
@Slf4j
public class RerankerService {

    @Autowired
    private BGERerankerModel bgeReranker;

    @Autowired
    private ColBERTModel colbertModel;

    @Autowired
    private RerankerCache rerankerCache;

    @Autowired
    private RerankerConfig config;

    public RerankResult rerank(RerankRequest request) {
        try {
            // 1. 检查缓存
            String cacheKey = generateRerankCacheKey(request);
            RerankResult cachedResult = rerankerCache.getCachedResult(cacheKey);
            if (cachedResult != null) {
                return cachedResult;
            }

            // 2. 选择重排策略
            RerankStrategy strategy = selectRerankStrategy(request);

            // 3. 执行重排
            RerankResult result = executeRerank(request, strategy);

            // 4. 缓存结果
            if (result.isSuccess()) {
                rerankerCache.cacheResult(cacheKey, result);
            }

            return result;

        } catch (Exception e) {
            log.error("重排序失败", e);
            return RerankResult.failure("重排序失败: " + e.getMessage());
        }
    }

    private RerankStrategy selectRerankStrategy(RerankRequest request) {
        int candidateCount = request.getCandidates().size();

        if (candidateCount <= 10) {
            // 少量候选，使用高精度模型
            return RerankStrategy.BGE_LARGE;
        } else if (candidateCount <= 50) {
            // 中等数量，使用平衡模型
            return RerankStrategy.BGE_BASE;
        } else {
            // 大量候选，使用快速模型
            return RerankStrategy.COLBERT;
        }
    }

    private RerankResult executeRerank(RerankRequest request, RerankStrategy strategy) {
        switch (strategy) {
            case BGE_LARGE:
            case BGE_BASE:
                return executeBGERerank(request, strategy);
            case COLBERT:
                return executeColBERTRerank(request);
            case BATCH:
                return executeBatchRerank(request);
            default:
                throw new UnsupportedOperationException("不支持的重排策略: " + strategy);
        }
    }

    private RerankResult executeBGERerank(RerankRequest request, RerankStrategy strategy) {
        try {
            List<RetrievalResult> candidates = request.getCandidates();
            String query = request.getQuery();

            // 批量重排
            List<RerankCandidate> rerankCandidates = candidates.stream()
                .map(candidate -> RerankCandidate.builder()
                    .id(candidate.getId())
                    .text(candidate.getContent())
                    .originalScore(candidate.getScore())
                    .build())
                .collect(Collectors.toList());

            BGERerankRequest bgeRequest = BGERerankRequest.builder()
                .query(query)
                .candidates(rerankCandidates)
                .modelType(strategy == RerankStrategy.BGE_LARGE ? "large" : "base")
                .build();

            BGERerankResponse bgeResponse = bgeReranker.rerank(bgeRequest);

            if (bgeResponse.isSuccess()) {
                // 更新候选结果的分数
                List<RetrievalResult> rerankedResults = updateScores(candidates, bgeResponse.getScores());

                return RerankResult.builder()
                    .rerankedResults(rerankedResults)
                    .originalCount(candidates.size())
                    .strategy(strategy)
                    .success(true)
                    .build();
            } else {
                return RerankResult.failure("BGE重排失败: " + bgeResponse.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("BGE重排执行失败", e);
            return RerankResult.failure("BGE重排执行失败: " + e.getMessage());
        }
    }

    private RerankResult executeColBERTRerank(RerankRequest request) {
        try {
            ColBERTRerankRequest colbertRequest = ColBERTRerankRequest.builder()
                .query(request.getQuery())
                .documents(request.getCandidates().stream()
                    .map(RetrievalResult::getContent)
                    .collect(Collectors.toList()))
                .build();

            ColBERTRerankResponse colbertResponse = colbertModel.rerank(colbertRequest);

            if (colbertResponse.isSuccess()) {
                List<RetrievalResult> rerankedResults = updateScores(
                    request.getCandidates(), colbertResponse.getScores());

                return RerankResult.builder()
                    .rerankedResults(rerankedResults)
                    .originalCount(request.getCandidates().size())
                    .strategy(RerankStrategy.COLBERT)
                    .success(true)
                    .build();
            } else {
                return RerankResult.failure("ColBERT重排失败: " + colbertResponse.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("ColBERT重排执行失败", e);
            return RerankResult.failure("ColBERT重排执行失败: " + e.getMessage());
        }
    }

    private RerankResult executeBatchRerank(RerankRequest request) {
        List<RetrievalResult> candidates = request.getCandidates();
        int batchSize = config.getBatchSize();

        List<RetrievalResult> allRerankedResults = new ArrayList<>();

        // 分批处理
        for (int i = 0; i < candidates.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, candidates.size());
            List<RetrievalResult> batch = candidates.subList(i, endIndex);

            RerankRequest batchRequest = request.toBuilder()
                .candidates(batch)
                .build();

            RerankResult batchResult = executeBGERerank(batchRequest, RerankStrategy.BGE_BASE);

            if (batchResult.isSuccess()) {
                allRerankedResults.addAll(batchResult.getRerankedResults());
            } else {
                // 批次失败，保留原始结果
                allRerankedResults.addAll(batch);
            }
        }

        // 全局排序
        allRerankedResults.sort((r1, r2) -> Double.compare(r2.getScore(), r1.getScore()));

        return RerankResult.builder()
            .rerankedResults(allRerankedResults)
            .originalCount(candidates.size())
            .strategy(RerankStrategy.BATCH)
            .success(true)
            .build();
    }

    private List<RetrievalResult> updateScores(List<RetrievalResult> candidates, List<Double> newScores) {
        List<RetrievalResult> updatedResults = new ArrayList<>();

        for (int i = 0; i < candidates.size() && i < newScores.size(); i++) {
            RetrievalResult original = candidates.get(i);
            RetrievalResult updated = original.copy();
            updated.setScore(newScores.get(i));
            updated.setOriginalScore(original.getScore());
            updatedResults.add(updated);
        }

        // 按新分数排序
        updatedResults.sort((r1, r2) -> Double.compare(r2.getScore(), r1.getScore()));

        return updatedResults;
    }

    private String generateRerankCacheKey(RerankRequest request) {
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(DigestUtils.sha256Hex(request.getQuery()));
        keyBuilder.append(":");

        // 候选结果的哈希
        String candidatesHash = request.getCandidates().stream()
            .map(candidate -> candidate.getId() + ":" + candidate.getScore())
            .collect(Collectors.joining(","));
        keyBuilder.append(DigestUtils.sha256Hex(candidatesHash));

        return keyBuilder.toString();
    }
}
```

## 15. 上下文完整性保障

### 15.1 分段上下文扩展

```java
@Service
@Slf4j
public class ContextIntegrityService {

    @Autowired
    private TextChunkService textChunkService;

    @Autowired
    private SemanticBoundaryDetector boundaryDetector;

    @Autowired
    private ContextWindowManager windowManager;

    public ContextExpansionResult expandContext(ContextExpansionRequest request) {
        try {
            List<RetrievalResult> originalResults = request.getRetrievalResults();
            List<RetrievalResult> expandedResults = new ArrayList<>();

            for (RetrievalResult result : originalResults) {
                ExpandedContext expandedContext = expandSingleContext(result, request.getExpansionConfig());

                RetrievalResult expandedResult = result.copy();
                expandedResult.setContent(expandedContext.getExpandedContent());
                expandedResult.setMetadata(mergeMetadata(result.getMetadata(), expandedContext.getMetadata()));

                expandedResults.add(expandedResult);
            }

            return ContextExpansionResult.builder()
                .expandedResults(expandedResults)
                .originalCount(originalResults.size())
                .expansionStrategy(request.getExpansionConfig().getStrategy())
                .success(true)
                .build();

        } catch (Exception e) {
            log.error("上下文扩展失败", e);
            return ContextExpansionResult.failure("上下文扩展失败: " + e.getMessage());
        }
    }

    private ExpandedContext expandSingleContext(RetrievalResult result, ContextExpansionConfig config) {
        String chunkId = result.getId();
        TextChunk originalChunk = textChunkService.getChunkById(chunkId);

        if (originalChunk == null) {
            return ExpandedContext.builder()
                .expandedContent(result.getContent())
                .metadata(Collections.emptyMap())
                .build();
        }

        // 1. 获取相邻分段
        List<TextChunk> adjacentChunks = getAdjacentChunks(originalChunk, config);

        // 2. 检测语义边界
        List<TextChunk> semanticallyCoherentChunks = filterSemanticallyCohesive(
            adjacentChunks, originalChunk, config);

        // 3. 构建扩展上下文
        String expandedContent = buildExpandedContent(semanticallyCoherentChunks, originalChunk, config);

        // 4. 管理上下文窗口
        String finalContent = windowManager.manageContextWindow(expandedContent, config);

        return ExpandedContext.builder()
            .expandedContent(finalContent)
            .metadata(buildExpansionMetadata(semanticallyCoherentChunks, originalChunk))
            .build();
    }

    private List<TextChunk> getAdjacentChunks(TextChunk originalChunk, ContextExpansionConfig config) {
        List<TextChunk> adjacentChunks = new ArrayList<>();

        // 获取前向分段
        List<TextChunk> precedingChunks = textChunkService.getPrecedingChunks(
            originalChunk.getDocumentId(),
            originalChunk.getChunkIndex(),
            config.getPrecedingChunkCount()
        );

        // 获取后向分段
        List<TextChunk> followingChunks = textChunkService.getFollowingChunks(
            originalChunk.getDocumentId(),
            originalChunk.getChunkIndex(),
            config.getFollowingChunkCount()
        );

        adjacentChunks.addAll(precedingChunks);
        adjacentChunks.add(originalChunk);
        adjacentChunks.addAll(followingChunks);

        return adjacentChunks;
    }

    private List<TextChunk> filterSemanticallyCohesive(
            List<TextChunk> chunks, TextChunk originalChunk, ContextExpansionConfig config) {

        if (!config.isEnableSemanticFiltering()) {
            return chunks;
        }

        List<TextChunk> cohesiveChunks = new ArrayList<>();

        for (TextChunk chunk : chunks) {
            if (chunk.getId().equals(originalChunk.getId())) {
                // 原始分段总是包含
                cohesiveChunks.add(chunk);
            } else {
                // 检查语义连贯性
                double coherenceScore = boundaryDetector.calculateCoherence(originalChunk, chunk);

                if (coherenceScore >= config.getSemanticThreshold()) {
                    cohesiveChunks.add(chunk);
                } else {
                    log.debug("分段{}与原始分段语义连贯性不足: {}", chunk.getId(), coherenceScore);
                }
            }
        }

        return cohesiveChunks;
    }

    private String buildExpandedContent(List<TextChunk> chunks, TextChunk originalChunk, ContextExpansionConfig config) {
        StringBuilder expandedContent = new StringBuilder();

        // 按分段顺序排序
        chunks.sort(Comparator.comparing(TextChunk::getChunkIndex));

        for (int i = 0; i < chunks.size(); i++) {
            TextChunk chunk = chunks.get(i);

            // 标记原始分段
            if (chunk.getId().equals(originalChunk.getId())) {
                if (config.isHighlightOriginal()) {
                    expandedContent.append("【核心内容】");
                }
                expandedContent.append(chunk.getContent());
                if (config.isHighlightOriginal()) {
                    expandedContent.append("【/核心内容】");
                }
            } else {
                // 添加上下文标记
                if (chunk.getChunkIndex() < originalChunk.getChunkIndex()) {
                    if (config.isShowContextMarkers()) {
                        expandedContent.append("【前文】");
                    }
                    expandedContent.append(chunk.getContent());
                    if (config.isShowContextMarkers()) {
                        expandedContent.append("【/前文】");
                    }
                } else {
                    if (config.isShowContextMarkers()) {
                        expandedContent.append("【后文】");
                    }
                    expandedContent.append(chunk.getContent());
                    if (config.isShowContextMarkers()) {
                        expandedContent.append("【/后文】");
                    }
                }
            }

            // 添加分段间分隔符
            if (i < chunks.size() - 1) {
                expandedContent.append(config.getChunkSeparator());
            }
        }

        return expandedContent.toString();
    }

    private Map<String, Object> buildExpansionMetadata(List<TextChunk> expandedChunks, TextChunk originalChunk) {
        Map<String, Object> metadata = new HashMap<>();

        metadata.put("original_chunk_id", originalChunk.getId());
        metadata.put("expanded_chunk_count", expandedChunks.size());
        metadata.put("expanded_chunk_ids", expandedChunks.stream()
            .map(TextChunk::getId)
            .collect(Collectors.toList()));

        // 计算扩展范围
        int minIndex = expandedChunks.stream().mapToInt(TextChunk::getChunkIndex).min().orElse(0);
        int maxIndex = expandedChunks.stream().mapToInt(TextChunk::getChunkIndex).max().orElse(0);

        metadata.put("expansion_range", Map.of(
            "start_index", minIndex,
            "end_index", maxIndex,
            "original_index", originalChunk.getChunkIndex()
        ));

        return metadata;
    }

    private Map<String, Object> mergeMetadata(Map<String, Object> originalMetadata, Map<String, Object> expansionMetadata) {
        Map<String, Object> mergedMetadata = new HashMap<>(originalMetadata);
        mergedMetadata.putAll(expansionMetadata);
        return mergedMetadata;
    }
}

@Component
@Slf4j
public class SemanticBoundaryDetector {

    @Autowired
    private EmbeddingModel embeddingModel;

    @Autowired
    private SimilarityCalculator similarityCalculator;

    public double calculateCoherence(TextChunk chunk1, TextChunk chunk2) {
        try {
            // 1. 计算向量相似度
            float[] vector1 = embeddingModel.embed(chunk1.getContent());
            float[] vector2 = embeddingModel.embed(chunk2.getContent());

            double vectorSimilarity = similarityCalculator.cosineSimilarity(vector1, vector2);

            // 2. 计算词汇重叠度
            double lexicalOverlap = calculateLexicalOverlap(chunk1.getContent(), chunk2.getContent());

            // 3. 计算位置连续性
            double positionContinuity = calculatePositionContinuity(chunk1, chunk2);

            // 4. 综合计算连贯性分数
            return vectorSimilarity * 0.6 + lexicalOverlap * 0.2 + positionContinuity * 0.2;

        } catch (Exception e) {
            log.warn("计算语义连贯性失败: {} <-> {}", chunk1.getId(), chunk2.getId(), e);
            return 0.0;
        }
    }

    private double calculateLexicalOverlap(String content1, String content2) {
        Set<String> words1 = new HashSet<>(Arrays.asList(content1.toLowerCase().split("\\s+")));
        Set<String> words2 = new HashSet<>(Arrays.asList(content2.toLowerCase().split("\\s+")));

        Set<String> intersection = new HashSet<>(words1);
        intersection.retainAll(words2);

        Set<String> union = new HashSet<>(words1);
        union.addAll(words2);

        return union.isEmpty() ? 0.0 : (double) intersection.size() / union.size();
    }

    private double calculatePositionContinuity(TextChunk chunk1, TextChunk chunk2) {
        int indexDiff = Math.abs(chunk1.getChunkIndex() - chunk2.getChunkIndex());

        // 相邻分段连续性最高
        if (indexDiff == 1) {
            return 1.0;
        } else if (indexDiff <= 3) {
            return 1.0 - (indexDiff - 1) * 0.2;
        } else {
            return 0.2;
        }
    }
}

@Component
@Slf4j
public class ContextWindowManager {

    public String manageContextWindow(String expandedContent, ContextExpansionConfig config) {
        int maxLength = config.getMaxContextLength();

        if (expandedContent.length() <= maxLength) {
            return expandedContent;
        }

        // 动态调整上下文长度
        return adjustContextLength(expandedContent, maxLength, config);
    }

    private String adjustContextLength(String content, int maxLength, ContextExpansionConfig config) {
        // 1. 尝试保留核心内容
        String coreContent = extractCoreContent(content);

        if (coreContent.length() >= maxLength) {
            // 核心内容过长，截断核心内容
            return truncateContent(coreContent, maxLength);
        }

        // 2. 在剩余空间内添加上下文
        int remainingSpace = maxLength - coreContent.length();
        String contextContent = extractContextContent(content, coreContent);

        if (contextContent.length() <= remainingSpace) {
            return coreContent + contextContent;
        } else {
            // 按比例分配前文和后文
            String truncatedContext = truncateContextProportionally(contextContent, remainingSpace, config);
            return coreContent + truncatedContext;
        }
    }

    private String extractCoreContent(String content) {
        // 提取【核心内容】标记的内容
        Pattern pattern = Pattern.compile("【核心内容】(.*?)【/核心内容】", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(content);

        if (matcher.find()) {
            return matcher.group(1);
        }

        // 如果没有标记，返回原内容的中间部分
        return content.substring(0, Math.min(content.length(), content.length() / 2));
    }

    private String extractContextContent(String content, String coreContent) {
        return content.replace(coreContent, "");
    }

    private String truncateContent(String content, int maxLength) {
        if (content.length() <= maxLength) {
            return content;
        }

        // 在句子边界截断
        int truncatePoint = maxLength;
        for (int i = maxLength - 1; i >= maxLength - 100 && i >= 0; i--) {
            char c = content.charAt(i);
            if (c == '。' || c == '！' || c == '？' || c == '\n') {
                truncatePoint = i + 1;
                break;
            }
        }

        return content.substring(0, truncatePoint) + "...";
    }

    private String truncateContextProportionally(String contextContent, int maxLength, ContextExpansionConfig config) {
        // 按前文后文比例分配空间
        double precedingRatio = config.getPrecedingContextRatio();
        double followingRatio = 1.0 - precedingRatio;

        int precedingLength = (int) (maxLength * precedingRatio);
        int followingLength = maxLength - precedingLength;

        String precedingContent = extractPrecedingContent(contextContent);
        String followingContent = extractFollowingContent(contextContent);

        String truncatedPreceding = truncateContent(precedingContent, precedingLength);
        String truncatedFollowing = truncateContent(followingContent, followingLength);

        return truncatedPreceding + truncatedFollowing;
    }

    private String extractPrecedingContent(String contextContent) {
        Pattern pattern = Pattern.compile("【前文】(.*?)【/前文】", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(contextContent);

        StringBuilder preceding = new StringBuilder();
        while (matcher.find()) {
            preceding.append(matcher.group(1));
        }

        return preceding.toString();
    }

    private String extractFollowingContent(String contextContent) {
        Pattern pattern = Pattern.compile("【后文】(.*?)【/后文】", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(contextContent);

        StringBuilder following = new StringBuilder();
        while (matcher.find()) {
            following.append(matcher.group(1));
        }

        return following.toString();
    }
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContextExpansionConfig {

    @Builder.Default
    private ContextExpansionStrategy strategy = ContextExpansionStrategy.SEMANTIC_AWARE;

    @Builder.Default
    private Integer precedingChunkCount = 2;

    @Builder.Default
    private Integer followingChunkCount = 2;

    @Builder.Default
    private Boolean enableSemanticFiltering = true;

    @Builder.Default
    private Double semanticThreshold = 0.6;

    @Builder.Default
    private Integer maxContextLength = 2000;

    @Builder.Default
    private Boolean highlightOriginal = true;

    @Builder.Default
    private Boolean showContextMarkers = false;

    @Builder.Default
    private String chunkSeparator = "\n\n";

    @Builder.Default
    private Double precedingContextRatio = 0.4;
}

public enum ContextExpansionStrategy {
    FIXED_WINDOW("固定窗口"),
    SEMANTIC_AWARE("语义感知"),
    ADAPTIVE("自适应"),
    BOUNDARY_BASED("边界检测");

    private final String description;

    ContextExpansionStrategy(String description) {
        this.description = description;
    }
}
```

---

**向量化与检索模块设计已完善，包含了完整的11个核心功能模块的技术实现细节。**
