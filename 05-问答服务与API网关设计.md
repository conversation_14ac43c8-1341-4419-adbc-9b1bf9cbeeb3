# 企业级RAG知识库系统 - 问答服务与API网关设计

## 1. 问答服务设计

### 1.1 服务概述

问答服务是RAG系统的核心业务模块，负责整合检索结果和大语言模型，为用户提供准确、相关的智能问答服务。

### 1.2 核心功能

- **多模型支持**：OpenAI、Claude、国产大模型等
- **上下文管理**：对话历史和检索上下文的智能管理
- **答案生成**：基于检索增强的高质量答案生成
- **流式响应**：支持流式输出，提升用户体验
- **答案评估**：自动评估答案质量和相关性

### 1.3 技术架构

```mermaid
graph TB
    subgraph "问答服务"
        QAController[问答控制器]
        QAManager[问答管理器]
        ContextBuilder[上下文构建器]
        
        subgraph "LLM适配器"
            OpenAIAdapter[OpenAI适配器]
            ClaudeAdapter[Claude适配器]
            LocalLLMAdapter[本地模型适配器]
            QwenAdapter[通义千问适配器]
        end
        
        subgraph "答案处理"
            AnswerGenerator[答案生成器]
            AnswerEvaluator[答案评估器]
            AnswerCache[答案缓存]
        end
        
        subgraph "对话管理"
            SessionManager[会话管理器]
            HistoryManager[历史管理器]
        end
    end
    
    subgraph "外部服务"
        SearchService[检索服务]
        LLMAPIs[大模型APIs]
    end
    
    subgraph "存储"
        Redis[(Redis缓存)]
        MySQL[(会话存储)]
    end
    
    QAController --> QAManager
    QAManager --> ContextBuilder
    QAManager --> SessionManager
    
    ContextBuilder --> SearchService
    
    QAManager --> AnswerGenerator
    AnswerGenerator --> OpenAIAdapter
    AnswerGenerator --> ClaudeAdapter
    AnswerGenerator --> LocalLLMAdapter
    AnswerGenerator --> QwenAdapter
    
    OpenAIAdapter --> LLMAPIs
    ClaudeAdapter --> LLMAPIs
    QwenAdapter --> LLMAPIs
    
    AnswerGenerator --> AnswerEvaluator
    AnswerEvaluator --> AnswerCache
    
    SessionManager --> HistoryManager
    HistoryManager --> Redis
    HistoryManager --> MySQL
```

### 1.4 问答管理器实现

```java
@Service
@Slf4j
public class QAManager {
    
    @Autowired
    private ContextBuilder contextBuilder;
    
    @Autowired
    private LLMModelRouter llmRouter;
    
    @Autowired
    private SessionManager sessionManager;
    
    @Autowired
    private AnswerEvaluator answerEvaluator;
    
    @Autowired
    private AnswerCacheService answerCache;
    
    /**
     * 问答主流程
     */
    public QAResponse processQuestion(QARequest request) {
        String sessionId = request.getSessionId();
        String question = request.getQuestion();
        
        try {
            // 1. 检查答案缓存
            String cacheKey = generateCacheKey(question, request.getKnowledgeBaseId());
            QAResponse cachedAnswer = answerCache.getAnswer(cacheKey);
            if (cachedAnswer != null && isAnswerValid(cachedAnswer)) {
                log.debug("命中答案缓存: {}", cacheKey);
                return cachedAnswer;
            }
            
            // 2. 构建检索上下文
            RetrievalContext retrievalContext = contextBuilder.buildContext(request);
            
            // 3. 获取对话历史
            ConversationHistory history = sessionManager.getHistory(sessionId);
            
            // 4. 构建完整上下文
            FullContext fullContext = FullContext.builder()
                .question(question)
                .retrievalContext(retrievalContext)
                .conversationHistory(history)
                .userPreferences(request.getUserPreferences())
                .build();
            
            // 5. 生成答案
            LLMModel model = llmRouter.selectModel(request.getModelPreference());
            GenerationResult result = model.generate(fullContext);
            
            // 6. 评估答案质量
            AnswerQuality quality = answerEvaluator.evaluate(question, result.getAnswer(), retrievalContext);
            
            // 7. 构建响应
            QAResponse response = QAResponse.builder()
                .answer(result.getAnswer())
                .sources(retrievalContext.getSources())
                .confidence(quality.getConfidence())
                .responseTime(result.getResponseTime())
                .modelUsed(model.getModelName())
                .sessionId(sessionId)
                .build();
            
            // 8. 更新会话历史
            sessionManager.addToHistory(sessionId, question, response.getAnswer());
            
            // 9. 缓存答案（如果质量足够好）
            if (quality.getConfidence() > 0.8) {
                answerCache.putAnswer(cacheKey, response);
            }
            
            return response;
            
        } catch (Exception e) {
            log.error("问答处理失败: {}", question, e);
            return QAResponse.error("问答处理失败: " + e.getMessage());
        }
    }
    
    /**
     * 流式问答处理
     */
    public Flux<QAStreamResponse> processQuestionStream(QARequest request) {
        return Flux.create(sink -> {
            try {
                // 构建上下文（同上）
                RetrievalContext retrievalContext = contextBuilder.buildContext(request);
                ConversationHistory history = sessionManager.getHistory(request.getSessionId());
                
                FullContext fullContext = FullContext.builder()
                    .question(request.getQuestion())
                    .retrievalContext(retrievalContext)
                    .conversationHistory(history)
                    .build();
                
                // 流式生成
                LLMModel model = llmRouter.selectModel(request.getModelPreference());
                model.generateStream(fullContext, new StreamCallback() {
                    @Override
                    public void onToken(String token) {
                        sink.next(QAStreamResponse.token(token));
                    }
                    
                    @Override
                    public void onComplete(String fullAnswer) {
                        // 更新会话历史
                        sessionManager.addToHistory(request.getSessionId(), 
                                                   request.getQuestion(), fullAnswer);
                        sink.next(QAStreamResponse.complete(fullAnswer));
                        sink.complete();
                    }
                    
                    @Override
                    public void onError(Throwable error) {
                        sink.error(error);
                    }
                });
                
            } catch (Exception e) {
                sink.error(e);
            }
        });
    }
}
```

### 1.5 上下文构建器

```java
@Component
@Slf4j
public class ContextBuilder {
    
    @Autowired
    private SearchService searchService;
    
    @Value("${qa.context.max-chunks:5}")
    private int maxChunks;
    
    @Value("${qa.context.max-length:4000}")
    private int maxContextLength;
    
    public RetrievalContext buildContext(QARequest request) {
        try {
            // 1. 执行检索
            SearchRequest searchRequest = SearchRequest.builder()
                .query(request.getQuestion())
                .knowledgeBaseId(request.getKnowledgeBaseId())
                .topK(maxChunks * 2) // 检索更多候选，后续筛选
                .filters(request.getFilters())
                .build();
            
            SearchResponse searchResponse = searchService.hybridSearch(searchRequest);
            
            // 2. 筛选和排序检索结果
            List<SearchResult> filteredResults = filterAndRankResults(
                searchResponse.getResults(), request.getQuestion());
            
            // 3. 构建上下文文本
            String contextText = buildContextText(filteredResults);
            
            // 4. 提取来源信息
            List<SourceInfo> sources = extractSources(filteredResults);
            
            return RetrievalContext.builder()
                .contextText(contextText)
                .sources(sources)
                .retrievalScore(calculateRetrievalScore(filteredResults))
                .chunkCount(filteredResults.size())
                .build();
            
        } catch (Exception e) {
            log.error("构建检索上下文失败", e);
            return RetrievalContext.empty();
        }
    }
    
    private List<SearchResult> filterAndRankResults(List<SearchResult> results, String question) {
        return results.stream()
            // 过滤低质量结果
            .filter(result -> result.getScore() > 0.6)
            // 去重相似内容
            .filter(this::isNotDuplicate)
            // 按相关性排序
            .sorted((r1, r2) -> Double.compare(r2.getScore(), r1.getScore()))
            // 限制数量
            .limit(maxChunks)
            .collect(Collectors.toList());
    }
    
    private String buildContextText(List<SearchResult> results) {
        StringBuilder context = new StringBuilder();
        int currentLength = 0;
        
        for (int i = 0; i < results.size(); i++) {
            SearchResult result = results.get(i);
            String chunk = String.format("[文档%d] %s\n\n", i + 1, result.getContent());
            
            if (currentLength + chunk.length() > maxContextLength) {
                break;
            }
            
            context.append(chunk);
            currentLength += chunk.length();
        }
        
        return context.toString();
    }
}
```

### 1.6 LLM模型适配器

```java
@Component
@Slf4j
public class OpenAILLMAdapter implements LLMModel {
    
    @Autowired
    private OpenAIClient openAIClient;
    
    @Value("${llm.openai.model:gpt-4}")
    private String modelName;
    
    @Value("${llm.openai.temperature:0.7}")
    private double temperature;
    
    @Value("${llm.openai.max-tokens:2000}")
    private int maxTokens;
    
    @Override
    public String getModelName() {
        return "openai-" + modelName;
    }
    
    @Override
    public GenerationResult generate(FullContext context) {
        try {
            long startTime = System.currentTimeMillis();
            
            // 构建提示词
            String prompt = buildPrompt(context);
            
            // 调用OpenAI API
            ChatCompletionRequest request = ChatCompletionRequest.builder()
                .model(modelName)
                .messages(Arrays.asList(
                    ChatMessage.builder()
                        .role("system")
                        .content(getSystemPrompt())
                        .build(),
                    ChatMessage.builder()
                        .role("user")
                        .content(prompt)
                        .build()
                ))
                .temperature(temperature)
                .maxTokens(maxTokens)
                .build();
            
            ChatCompletionResponse response = openAIClient.createChatCompletion(request);
            
            String answer = response.getChoices().get(0).getMessage().getContent();
            long responseTime = System.currentTimeMillis() - startTime;
            
            return GenerationResult.builder()
                .answer(answer)
                .responseTime(responseTime)
                .tokenUsage(response.getUsage())
                .build();
            
        } catch (Exception e) {
            log.error("OpenAI生成失败", e);
            throw new LLMGenerationException("OpenAI调用失败", e);
        }
    }
    
    @Override
    public void generateStream(FullContext context, StreamCallback callback) {
        try {
            String prompt = buildPrompt(context);
            
            ChatCompletionRequest request = ChatCompletionRequest.builder()
                .model(modelName)
                .messages(Arrays.asList(
                    ChatMessage.builder()
                        .role("system")
                        .content(getSystemPrompt())
                        .build(),
                    ChatMessage.builder()
                        .role("user")
                        .content(prompt)
                        .build()
                ))
                .temperature(temperature)
                .maxTokens(maxTokens)
                .stream(true)
                .build();
            
            StringBuilder fullAnswer = new StringBuilder();
            
            openAIClient.createChatCompletionStream(request, new StreamResponseHandler() {
                @Override
                public void onToken(String token) {
                    fullAnswer.append(token);
                    callback.onToken(token);
                }
                
                @Override
                public void onComplete() {
                    callback.onComplete(fullAnswer.toString());
                }
                
                @Override
                public void onError(Throwable error) {
                    callback.onError(error);
                }
            });
            
        } catch (Exception e) {
            callback.onError(e);
        }
    }
    
    private String buildPrompt(FullContext context) {
        StringBuilder prompt = new StringBuilder();
        
        // 添加检索上下文
        if (context.getRetrievalContext() != null && 
            StringUtils.isNotBlank(context.getRetrievalContext().getContextText())) {
            prompt.append("参考信息：\n");
            prompt.append(context.getRetrievalContext().getContextText());
            prompt.append("\n\n");
        }
        
        // 添加对话历史
        if (context.getConversationHistory() != null && 
            !context.getConversationHistory().isEmpty()) {
            prompt.append("对话历史：\n");
            context.getConversationHistory().getRecentMessages(3).forEach(msg -> {
                prompt.append("用户: ").append(msg.getQuestion()).append("\n");
                prompt.append("助手: ").append(msg.getAnswer()).append("\n\n");
            });
        }
        
        // 添加当前问题
        prompt.append("问题：").append(context.getQuestion()).append("\n\n");
        prompt.append("请基于上述参考信息回答问题。如果参考信息不足以回答问题，请明确说明。");
        
        return prompt.toString();
    }
    
    private String getSystemPrompt() {
        return "你是一个专业的知识库助手。请基于提供的参考信息准确回答用户问题。" +
               "回答要求：1) 准确性优先 2) 引用具体信息 3) 承认知识边界 4) 语言简洁清晰";
    }
}
```

## 2. API网关设计

### 2.1 网关概述

API网关作为系统的统一入口，负责请求路由、认证授权、限流熔断、监控日志等功能。

### 2.2 核心功能

- **请求路由**：智能路由到后端微服务
- **认证授权**：JWT Token验证和RBAC权限控制
- **限流熔断**：防止系统过载和雪崩
- **监控日志**：请求链路追踪和性能监控
- **协议转换**：HTTP/WebSocket/gRPC协议支持

### 2.3 网关架构

```mermaid
graph TB
    subgraph "客户端"
        WebApp[Web应用]
        MobileApp[移动应用]
        ThirdParty[第三方API]
    end
    
    subgraph "API网关"
        LoadBalancer[负载均衡器]
        
        subgraph "网关核心"
            Router[路由器]
            AuthFilter[认证过滤器]
            RateLimiter[限流器]
            CircuitBreaker[熔断器]
            Logger[日志记录器]
        end
        
        subgraph "路由规则"
            DocumentRoute[文档服务路由]
            SearchRoute[检索服务路由]
            QARoute[问答服务路由]
            UserRoute[用户服务路由]
        end
    end
    
    subgraph "后端服务"
        DocumentService[文档服务]
        SearchService[检索服务]
        QAService[问答服务]
        UserService[用户服务]
        AuthService[认证服务]
    end
    
    subgraph "基础设施"
        Redis[(Redis)]
        Consul[服务发现]
    end
    
    WebApp --> LoadBalancer
    MobileApp --> LoadBalancer
    ThirdParty --> LoadBalancer
    
    LoadBalancer --> Router
    Router --> AuthFilter
    AuthFilter --> RateLimiter
    RateLimiter --> CircuitBreaker
    CircuitBreaker --> Logger
    
    Logger --> DocumentRoute
    Logger --> SearchRoute
    Logger --> QARoute
    Logger --> UserRoute
    
    DocumentRoute --> DocumentService
    SearchRoute --> SearchService
    QARoute --> QAService
    UserRoute --> UserService
    
    AuthFilter --> AuthService
    RateLimiter --> Redis
    Router --> Consul
```

### 2.4 网关配置

```yaml
# Spring Cloud Gateway配置
spring:
  cloud:
    gateway:
      routes:
        # 文档服务路由
        - id: document-service
          uri: lb://rag-document
          predicates:
            - Path=/api/documents/**
          filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 100
                redis-rate-limiter.burstCapacity: 200
            - name: CircuitBreaker
              args:
                name: document-circuit-breaker
                fallbackUri: forward:/fallback/document
        
        # 检索服务路由
        - id: search-service
          uri: lb://rag-search
          predicates:
            - Path=/api/search/**
          filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 200
                redis-rate-limiter.burstCapacity: 400
        
        # 问答服务路由
        - id: qa-service
          uri: lb://rag-qa
          predicates:
            - Path=/api/qa/**
          filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 50
                redis-rate-limiter.burstCapacity: 100
            - name: Retry
              args:
                retries: 3
                statuses: BAD_GATEWAY,GATEWAY_TIMEOUT
        
        # WebSocket路由（流式问答）
        - id: qa-websocket
          uri: lb:ws://rag-qa
          predicates:
            - Path=/ws/qa/**
      
      # 全局过滤器
      default-filters:
        - name: GlobalAuthenticationFilter
        - name: GlobalLoggingFilter
        - name: AddRequestHeader
          args:
            name: X-Request-Source
            value: gateway

# 限流配置
resilience4j:
  circuitbreaker:
    instances:
      document-circuit-breaker:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        sliding-window-size: 10
        minimum-number-of-calls: 5
  
  ratelimiter:
    instances:
      default:
        limit-for-period: 100
        limit-refresh-period: 1s
        timeout-duration: 0s
```

---

**下一步**：设计部署配置与实现指南
