# 企业级RAG知识库系统 - 系统整体架构设计

## 1. 系统概述

本系统是一个基于Java生态系统的企业级RAG（Retrieval Augmented Generation）知识库系统，采用微服务架构设计，支持大规模文档处理、智能检索和知识问答。

### 1.1 核心特性

- **🏗️ 微服务架构**：基于Spring Boot 3.x + Spring Cloud 2023.x
- **🔄 异步处理**：RocketMQ 5.x驱动的文档解析和向量化流水线
- **🎯 混合检索**：向量检索 + 关键词检索 + 重排序
- **📊 智能分块**：基于语义的文档分块策略
- **🚀 高性能**：Redis缓存 + Milvus向量存储优化
- **🔒 企业级**：完整的权限管理、审计日志、监控告警

### 1.2 技术栈

| 组件类型 | 技术选型 | 版本 | 说明 |
|---------|---------|------|------|
| 后端框架 | Spring Boot | 3.2+ | 微服务基础框架 |
| 微服务治理 | Spring Cloud | 2023.0+ | 服务发现、配置管理、网关 |
| 数据访问 | MyBatis-Plus | 3.5+ | ORM框架 |
| 缓存 | Redis | 7.0+ | 分布式缓存 |
| 消息队列 | RocketMQ | 5.1+ | 异步消息处理 |
| 任务调度 | XXL-Job | 2.4+ | 分布式任务调度 |
| 向量数据库 | Milvus | 2.3+ | 向量存储与检索 |
| 关系型数据库 | MySQL | 8.0+ | 业务数据存储 |
| 容器化 | Docker | 24.0+ | 容器化部署 |

## 2. 系统架构图

```mermaid
graph TB
    %% 用户层
    subgraph "用户层"
        WebUI[Web管理界面]
        MobileApp[移动端应用]
        API[第三方API调用]
    end

    %% 网关层
    subgraph "网关层"
        Gateway[Spring Cloud Gateway]
        Auth[认证授权服务]
    end

    %% 业务服务层
    subgraph "业务服务层"
        DocService[文档管理服务]
        ParseService[文档解析服务]
        VectorService[向量化服务]
        SearchService[检索服务]
        QAService[问答服务]
        UserService[用户管理服务]
    end

    %% Python微服务
    subgraph "Python微服务"
        OCRService[OCR识别服务]
        NLPService[NLP预处理服务]
        EmbeddingService[Embedding服务]
    end

    %% 数据存储层
    subgraph "数据存储层"
        MySQL[(MySQL 8.0)]
        Redis[(Redis 7.0)]
        Milvus[(Milvus 2.3)]
        MinIO[(MinIO对象存储)]
    end

    %% 消息队列
    subgraph "消息中间件"
        RocketMQ[RocketMQ 5.1]
    end

    %% 任务调度
    subgraph "任务调度"
        XXLJob[XXL-Job 2.4]
    end

    %% 外部服务
    subgraph "外部服务"
        LLMApi[大语言模型API]
        EmbeddingApi[Embedding模型API]
    end

    %% 连接关系
    WebUI --> Gateway
    MobileApp --> Gateway
    API --> Gateway
    
    Gateway --> Auth
    Gateway --> DocService
    Gateway --> SearchService
    Gateway --> QAService
    Gateway --> UserService
    
    DocService --> ParseService
    ParseService --> OCRService
    ParseService --> NLPService
    ParseService --> VectorService
    VectorService --> EmbeddingService
    VectorService --> EmbeddingApi
    
    SearchService --> Milvus
    SearchService --> Redis
    QAService --> LLMApi
    
    DocService --> MySQL
    UserService --> MySQL
    ParseService --> RocketMQ
    VectorService --> RocketMQ
    
    VectorService --> Milvus
    DocService --> MinIO
    
    XXLJob --> DocService
    XXLJob --> VectorService
```

## 3. 数据流架构图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Gateway as 网关
    participant DocSvc as 文档服务
    participant MQ as RocketMQ
    participant ParseSvc as 解析服务
    participant OCR as OCR服务
    participant VectorSvc as 向量服务
    participant Embedding as Embedding服务
    participant Milvus as Milvus
    participant SearchSvc as 检索服务
    participant QASvc as 问答服务
    participant LLM as 大模型API

    %% 文档上传流程
    User->>Gateway: 1. 上传文档
    Gateway->>DocSvc: 2. 文档管理
    DocSvc->>MQ: 3. 发送解析消息
    
    %% 文档解析流程
    MQ->>ParseSvc: 4. 消费解析消息
    ParseSvc->>OCR: 5. OCR识别(图片/PDF)
    OCR-->>ParseSvc: 6. 返回文本内容
    ParseSvc->>MQ: 7. 发送向量化消息
    
    %% 向量化流程
    MQ->>VectorSvc: 8. 消费向量化消息
    VectorSvc->>Embedding: 9. 文本向量化
    Embedding-->>VectorSvc: 10. 返回向量
    VectorSvc->>Milvus: 11. 存储向量
    
    %% 检索问答流程
    User->>Gateway: 12. 提交问题
    Gateway->>SearchSvc: 13. 检索请求
    SearchSvc->>Milvus: 14. 向量检索
    Milvus-->>SearchSvc: 15. 返回相关文档
    SearchSvc->>QASvc: 16. 构建上下文
    QASvc->>LLM: 17. 调用大模型
    LLM-->>QASvc: 18. 返回答案
    QASvc-->>User: 19. 返回最终答案
```

## 4. 微服务架构设计

### 4.1 服务拆分原则

1. **业务边界清晰**：按业务功能进行服务拆分
2. **数据独立**：每个服务拥有独立的数据存储
3. **技术异构**：支持不同技术栈（Java + Python）
4. **弹性扩展**：支持独立部署和扩展

### 4.2 核心微服务

| 服务名称 | 职责 | 技术栈 | 端口 |
|---------|------|--------|------|
| rag-gateway | API网关、路由、认证 | Spring Cloud Gateway | 8080 |
| rag-auth | 认证授权服务 | Spring Boot + JWT | 8081 |
| rag-document | 文档管理服务 | Spring Boot + MyBatis-Plus | 8082 |
| rag-parser | 文档解析服务 | Spring Boot + RocketMQ | 8083 |
| rag-vector | 向量化服务 | Spring Boot + Milvus | 8084 |
| rag-search | 检索服务 | Spring Boot + Milvus + Redis | 8085 |
| rag-qa | 问答服务 | Spring Boot + LLM API | 8086 |
| rag-user | 用户管理服务 | Spring Boot + MyBatis-Plus | 8087 |
| rag-ocr | OCR识别服务 | Python + PaddleOCR | 9001 |
| rag-nlp | NLP预处理服务 | Python + spaCy | 9002 |
| rag-embedding | Embedding服务 | Python + Transformers | 9003 |

### 4.3 服务间通信

1. **同步通信**：OpenFeign + LoadBalancer
2. **异步通信**：RocketMQ消息队列
3. **配置管理**：Spring Cloud Config
4. **服务发现**：Spring Cloud Consul/Nacos

## 5. 部署架构

### 5.1 容器化部署

```yaml
# docker-compose.yml 概览
version: '3.8'
services:
  # 基础设施
  mysql:
    image: mysql:8.0
  redis:
    image: redis:7.0
  milvus:
    image: milvusdb/milvus:v2.3.0
  rocketmq:
    image: apache/rocketmq:5.1.0
  
  # Java微服务
  rag-gateway:
    image: rag/gateway:latest
  rag-document:
    image: rag/document:latest
  
  # Python微服务
  rag-ocr:
    image: rag/ocr:latest
```

### 5.2 扩展性设计

1. **水平扩展**：支持服务实例动态扩缩容
2. **存储扩展**：Milvus集群部署，支持PB级向量数据
3. **计算扩展**：GPU加速的Embedding计算
4. **缓存扩展**：Redis集群，支持大规模缓存

## 6. 安全架构

### 6.1 认证授权

- **JWT Token**：无状态认证
- **RBAC权限模型**：角色基础访问控制
- **API限流**：防止恶意调用

### 6.2 数据安全

- **传输加密**：HTTPS/TLS
- **存储加密**：敏感数据加密存储
- **访问审计**：完整的操作日志

## 7. 监控与运维

### 7.1 监控体系

- **应用监控**：Spring Boot Actuator + Micrometer
- **基础设施监控**：Prometheus + Grafana
- **日志聚合**：ELK Stack
- **链路追踪**：Spring Cloud Sleuth + Zipkin

### 7.2 运维自动化

- **CI/CD**：Jenkins/GitLab CI
- **配置管理**：Spring Cloud Config
- **健康检查**：自动故障检测与恢复

---

**下一步**：详细设计各个核心模块的实现方案
