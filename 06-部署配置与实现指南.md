# 企业级RAG知识库系统 - 部署配置与实现指南

## 1. 开发环境搭建

### 1.1 环境要求

| 组件 | 版本要求 | 说明 |
|------|----------|------|
| JDK | 17+ | 支持Spring Boot 3.x |
| Maven | 3.8+ | 项目构建工具 |
| Docker | 24.0+ | 容器化部署 |
| Docker Compose | 2.20+ | 多容器编排 |
| Node.js | 18+ | 前端开发（可选） |

### 1.2 快速启动脚本

```bash
#!/bin/bash
# quick-start.sh - 一键启动开发环境

echo "🚀 启动RAG知识库系统开发环境..."

# 1. 检查Docker环境
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

# 2. 创建网络
docker network create rag-network 2>/dev/null || true

# 3. 启动基础设施
echo "📦 启动基础设施服务..."
docker-compose -f docker-compose.infrastructure.yml up -d

# 4. 等待服务就绪
echo "⏳ 等待服务启动..."
sleep 30

# 5. 初始化数据库
echo "🗄️ 初始化数据库..."
docker exec rag-mysql mysql -uroot -prag123456 -e "CREATE DATABASE IF NOT EXISTS rag_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 6. 启动应用服务
echo "🎯 启动应用服务..."
docker-compose -f docker-compose.services.yml up -d

echo "✅ 系统启动完成！"
echo "🌐 访问地址："
echo "   - API网关: http://localhost:8080"
echo "   - 管理界面: http://localhost:3000"
echo "   - Milvus管理: http://localhost:19121"
echo "   - XXL-Job控制台: http://localhost:8081/xxl-job-admin"
```

## 2. Docker Compose配置

### 2.1 基础设施服务配置

```yaml
# docker-compose.infrastructure.yml
version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: rag-mysql
    environment:
      MYSQL_ROOT_PASSWORD: rag123456
      MYSQL_DATABASE: rag_db
      MYSQL_USER: rag_user
      MYSQL_PASSWORD: rag_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - rag-network
    command: --default-authentication-plugin=mysql_native_password
    
  # Redis缓存
  redis:
    image: redis:7.0-alpine
    container_name: rag-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - rag-network
    command: redis-server --appendonly yes --requirepass rag123456
    
  # Milvus向量数据库
  etcd:
    image: quay.io/coreos/etcd:v3.5.5
    container_name: rag-etcd
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=4294967296
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - etcd_data:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    networks:
      - rag-network
      
  minio:
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    container_name: rag-minio
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    ports:
      - "9001:9001"
      - "9000:9000"
    volumes:
      - minio_data:/data
    command: minio server /data --console-address ":9001"
    networks:
      - rag-network
      
  milvus:
    image: milvusdb/milvus:v2.3.0
    container_name: rag-milvus
    command: ["milvus", "run", "standalone"]
    environment:
      ETCD_ENDPOINTS: etcd:2379
      MINIO_ADDRESS: minio:9000
    volumes:
      - milvus_data:/var/lib/milvus
      - ./config/milvus.yaml:/milvus/configs/milvus.yaml
    ports:
      - "19530:19530"
      - "19121:9091"
    depends_on:
      - etcd
      - minio
    networks:
      - rag-network
      
  # RocketMQ消息队列
  rocketmq-nameserver:
    image: apache/rocketmq:5.1.0
    container_name: rag-rocketmq-nameserver
    ports:
      - "9876:9876"
    volumes:
      - rocketmq_nameserver_data:/home/<USER>/logs
    command: ["sh", "mqnamesrv"]
    networks:
      - rag-network
      
  rocketmq-broker:
    image: apache/rocketmq:5.1.0
    container_name: rag-rocketmq-broker
    ports:
      - "10909:10909"
      - "10911:10911"
    volumes:
      - rocketmq_broker_data:/home/<USER>/logs
      - rocketmq_broker_store:/home/<USER>/store
      - ./config/broker.conf:/home/<USER>/rocketmq-5.1.0/conf/broker.conf
    command: ["sh", "mqbroker", "-n", "rocketmq-nameserver:9876", "-c", "/home/<USER>/rocketmq-5.1.0/conf/broker.conf"]
    depends_on:
      - rocketmq-nameserver
    networks:
      - rag-network

volumes:
  mysql_data:
  redis_data:
  etcd_data:
  minio_data:
  milvus_data:
  rocketmq_nameserver_data:
  rocketmq_broker_data:
  rocketmq_broker_store:

networks:
  rag-network:
    driver: bridge
```

### 2.2 应用服务配置

```yaml
# docker-compose.services.yml
version: '3.8'

services:
  # 服务发现
  consul:
    image: consul:1.15
    container_name: rag-consul
    ports:
      - "8500:8500"
    command: agent -server -ui -node=server-1 -bootstrap-expect=1 -client=0.0.0.0
    networks:
      - rag-network
      
  # API网关
  rag-gateway:
    build:
      context: ./rag-gateway
      dockerfile: Dockerfile
    container_name: rag-gateway
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - CONSUL_HOST=consul
      - REDIS_HOST=redis
    depends_on:
      - consul
      - redis
    networks:
      - rag-network
      
  # 认证服务
  rag-auth:
    build:
      context: ./rag-auth
      dockerfile: Dockerfile
    container_name: rag-auth
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
      - CONSUL_HOST=consul
    depends_on:
      - mysql
      - redis
      - consul
    networks:
      - rag-network
      
  # 文档服务
  rag-document:
    build:
      context: ./rag-document
      dockerfile: Dockerfile
    container_name: rag-document
    ports:
      - "8082:8082"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
      - MINIO_HOST=minio
      - ROCKETMQ_NAMESERVER=rocketmq-nameserver:9876
      - CONSUL_HOST=consul
    depends_on:
      - mysql
      - redis
      - minio
      - rocketmq-nameserver
      - consul
    networks:
      - rag-network
      
  # 解析服务
  rag-parser:
    build:
      context: ./rag-parser
      dockerfile: Dockerfile
    container_name: rag-parser
    ports:
      - "8083:8083"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - MYSQL_HOST=mysql
      - ROCKETMQ_NAMESERVER=rocketmq-nameserver:9876
      - CONSUL_HOST=consul
    depends_on:
      - mysql
      - rocketmq-nameserver
      - consul
    networks:
      - rag-network
      
  # 向量服务
  rag-vector:
    build:
      context: ./rag-vector
      dockerfile: Dockerfile
    container_name: rag-vector
    ports:
      - "8084:8084"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
      - MILVUS_HOST=milvus
      - ROCKETMQ_NAMESERVER=rocketmq-nameserver:9876
      - CONSUL_HOST=consul
    depends_on:
      - mysql
      - redis
      - milvus
      - rocketmq-nameserver
      - consul
    networks:
      - rag-network
      
  # 检索服务
  rag-search:
    build:
      context: ./rag-search
      dockerfile: Dockerfile
    container_name: rag-search
    ports:
      - "8085:8085"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
      - MILVUS_HOST=milvus
      - CONSUL_HOST=consul
    depends_on:
      - mysql
      - redis
      - milvus
      - consul
    networks:
      - rag-network
      
  # 问答服务
  rag-qa:
    build:
      context: ./rag-qa
      dockerfile: Dockerfile
    container_name: rag-qa
    ports:
      - "8086:8086"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
      - CONSUL_HOST=consul
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - CLAUDE_API_KEY=${CLAUDE_API_KEY}
    depends_on:
      - mysql
      - redis
      - consul
      - rag-search
    networks:
      - rag-network
      
  # Python OCR服务
  rag-ocr:
    build:
      context: ./rag-ocr-python
      dockerfile: Dockerfile
    container_name: rag-ocr
    ports:
      - "9001:9001"
    environment:
      - CONSUL_HOST=consul
    depends_on:
      - consul
    networks:
      - rag-network
      
  # Python Embedding服务
  rag-embedding:
    build:
      context: ./rag-embedding-python
      dockerfile: Dockerfile
    container_name: rag-embedding
    ports:
      - "9003:9003"
    environment:
      - CONSUL_HOST=consul
    depends_on:
      - consul
    networks:
      - rag-network
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

networks:
  rag-network:
    external: true
```

## 3. 核心代码示例

### 3.1 Spring Boot主启动类

```java
// rag-gateway/src/main/java/com/rag/gateway/RagGatewayApplication.java
@SpringBootApplication
@EnableDiscoveryClient
@EnableConfigurationProperties
public class RagGatewayApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(RagGatewayApplication.class, args);
    }
    
    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
        return builder.routes()
            .route("document-service", r -> r.path("/api/documents/**")
                .filters(f -> f.requestRateLimiter(c -> c.setRateLimiter(redisRateLimiter())))
                .uri("lb://rag-document"))
            .route("search-service", r -> r.path("/api/search/**")
                .uri("lb://rag-search"))
            .route("qa-service", r -> r.path("/api/qa/**")
                .uri("lb://rag-qa"))
            .build();
    }
    
    @Bean
    public RedisRateLimiter redisRateLimiter() {
        return new RedisRateLimiter(100, 200, 1);
    }
}
```

### 3.2 Milvus配置类

```java
// rag-vector/src/main/java/com/rag/vector/config/MilvusConfig.java
@Configuration
@EnableConfigurationProperties(MilvusProperties.class)
public class MilvusConfig {
    
    @Autowired
    private MilvusProperties milvusProperties;
    
    @Bean
    public MilvusClient milvusClient() {
        ConnectParam connectParam = ConnectParam.newBuilder()
            .withHost(milvusProperties.getHost())
            .withPort(milvusProperties.getPort())
            .withConnectTimeout(milvusProperties.getConnectTimeout())
            .withKeepAliveTime(milvusProperties.getKeepAliveTime())
            .withKeepAliveTimeout(milvusProperties.getKeepAliveTimeout())
            .withKeepAliveWithoutCalls(milvusProperties.isKeepAliveWithoutCalls())
            .withIdleTimeout(milvusProperties.getIdleTimeout())
            .build();
        
        return new MilvusServiceClient(connectParam);
    }
}

@ConfigurationProperties(prefix = "milvus")
@Data
public class MilvusProperties {
    private String host = "localhost";
    private int port = 19530;
    private long connectTimeout = 10000;
    private long keepAliveTime = 30000;
    private long keepAliveTimeout = 5000;
    private boolean keepAliveWithoutCalls = false;
    private long idleTimeout = 24 * 60 * 60 * 1000; // 24小时
}
```

### 3.3 RocketMQ配置类

```java
// rag-common/src/main/java/com/rag/common/config/RocketMQConfig.java
@Configuration
@EnableConfigurationProperties(RocketMQProperties.class)
public class RocketMQConfig {
    
    @Bean
    public RocketMQTemplate rocketMQTemplate() {
        RocketMQTemplate template = new RocketMQTemplate();
        template.setProducer(defaultMQProducer());
        return template;
    }
    
    @Bean
    public DefaultMQProducer defaultMQProducer() {
        DefaultMQProducer producer = new DefaultMQProducer();
        producer.setNamesrvAddr(rocketMQProperties.getNameServer());
        producer.setProducerGroup(rocketMQProperties.getProducer().getGroup());
        producer.setSendMsgTimeout(rocketMQProperties.getProducer().getSendTimeout());
        producer.setRetryTimesWhenSendFailed(rocketMQProperties.getProducer().getRetryTimes());
        return producer;
    }
}
```

## 4. 开发里程碑规划

### 4.1 第一阶段：基础框架搭建（2周）

**目标：** 完成基础架构和核心服务框架

**任务清单：**
- [ ] 项目结构搭建和Maven多模块配置
- [ ] Spring Boot微服务基础框架
- [ ] Docker开发环境配置
- [ ] 基础设施服务部署（MySQL、Redis、Milvus）
- [ ] 服务注册发现配置
- [ ] API网关基础功能

**验收标准：**
- 所有服务能够正常启动
- 服务间能够正常通信
- 基础的健康检查和监控

### 4.2 第二阶段：文档处理模块（3周）

**目标：** 实现文档上传、解析和预处理功能

**任务清单：**
- [ ] 文档上传服务实现
- [ ] 多格式文档解析器开发
- [ ] OCR服务集成
- [ ] 文本预处理和分块算法
- [ ] 异步处理流程实现
- [ ] 文档管理API开发

**验收标准：**
- 支持主要文档格式解析
- OCR识别准确率达到85%以上
- 文档处理性能满足需求

### 4.3 第三阶段：向量化与检索（3周）

**目标：** 实现向量化处理和混合检索功能

**任务清单：**
- [ ] Embedding模型集成
- [ ] Milvus向量存储实现
- [ ] 批量向量化处理
- [ ] 向量检索服务开发
- [ ] 关键词检索实现
- [ ] 混合检索和重排序算法

**验收标准：**
- 向量化处理性能达标
- 检索准确率达到80%以上
- 支持大规模向量数据

### 4.4 第四阶段：问答服务（2周）

**目标：** 实现智能问答和对话管理

**任务清单：**
- [ ] LLM模型适配器开发
- [ ] 上下文构建和管理
- [ ] 流式问答实现
- [ ] 对话历史管理
- [ ] 答案质量评估

**验收标准：**
- 问答准确率达到85%以上
- 支持流式响应
- 对话上下文管理正常

### 4.5 第五阶段：系统优化与部署（2周）

**目标：** 性能优化和生产环境部署

**任务清单：**
- [ ] 性能测试和优化
- [ ] 缓存策略完善
- [ ] 监控和日志系统
- [ ] 安全加固
- [ ] 生产环境部署配置
- [ ] 文档和培训材料

**验收标准：**
- 系统性能达到设计指标
- 安全测试通过
- 部署文档完整

## 5. 性能指标要求

### 5.1 系统性能指标

| 指标类型 | 目标值 | 说明 |
|----------|--------|------|
| 文档解析速度 | 1MB/秒 | 平均处理速度 |
| 向量化处理 | 1000条/分钟 | 文本块向量化速度 |
| 检索响应时间 | <500ms | 95%请求响应时间 |
| 问答响应时间 | <3秒 | 包含LLM调用时间 |
| 系统并发量 | 1000 QPS | 峰值并发处理能力 |
| 数据存储量 | 10TB+ | 支持的文档数据量 |

### 5.2 质量指标

| 指标类型 | 目标值 | 说明 |
|----------|--------|------|
| OCR识别准确率 | >85% | 图片文字识别准确率 |
| 检索准确率 | >80% | Top-5检索结果相关性 |
| 问答准确率 | >85% | 基于人工评估 |
| 系统可用性 | >99.5% | 年度可用性目标 |

---

**下一步**：创建项目结构和核心代码模板
