# 企业级RAG知识库系统

[![Java](https://img.shields.io/badge/Java-17+-orange.svg)](https://www.oracle.com/java/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.2+-green.svg)](https://spring.io/projects/spring-boot)
[![Milvus](https://img.shields.io/badge/Milvus-2.3+-blue.svg)](https://milvus.io/)
[![Docker](https://img.shields.io/badge/Docker-24.0+-blue.svg)](https://www.docker.com/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

> 基于Java生态系统的企业级RAG（Retrieval Augmented Generation）知识库系统，采用微服务架构，支持大规模文档处理、智能检索和知识问答。

## 🎯 项目特色

- **🏗️ 微服务架构**：Spring Boot 3.x + Spring Cloud 2023.x，易于扩展和维护
- **📊 智能处理**：支持PDF/Word/Excel/PPT/图片等多格式，集成OCR识别
- **🎯 混合检索**：向量检索 + 关键词检索 + BGE重排序，检索准确率>80%
- **🤖 多模型支持**：OpenAI、Claude、国产大模型等，问答准确率>85%
- **🚀 高性能**：Milvus向量存储 + Redis缓存 + RocketMQ异步处理
- **🔒 企业级**：完整的权限管理、监控告警、安全加固

## 📋 技术栈

| 层级 | 技术选型 | 版本 | 说明 |
|------|----------|------|------|
| **后端框架** | Spring Boot | 3.2+ | 微服务基础框架 |
| | Spring Cloud | 2023.0+ | 微服务治理 |
| | MyBatis-Plus | 3.5+ | ORM框架 |
| **数据存储** | MySQL | 8.0+ | 关系型数据库 |
| | Milvus | 2.3+ | 向量数据库 |
| | Redis | 7.0+ | 缓存数据库 |
| | MinIO | - | 对象存储 |
| **消息中间件** | RocketMQ | 5.1+ | 异步消息处理 |
| | XXL-Job | 2.4+ | 分布式任务调度 |
| **AI模型** | BGE-large-zh | - | 主力Embedding模型 |
| | OpenAI API | - | 备用Embedding + LLM |
| | 多种LLM | - | 问答生成 |
| **部署** | Docker | 24.0+ | 容器化部署 |
| | Docker Compose | - | 本地开发环境 |

## 🚀 快速开始

### 环境要求

- **Java**: JDK 17+
- **Maven**: 3.8+
- **Docker**: 24.0+
- **Docker Compose**: 2.20+

### 一键启动

```bash
# 1. 克隆项目
git clone <repository-url>
cd rag-knowledge-system

# 2. 给启动脚本执行权限
chmod +x quick-start.sh

# 3. 运行快速启动脚本
./quick-start.sh
```

### 手动启动

```bash
# 1. 创建Docker网络
docker network create rag-network

# 2. 启动基础设施服务
docker-compose -f docker-compose.infrastructure.yml up -d

# 3. 等待服务启动（约30秒）
sleep 30

# 4. 检查服务状态
docker-compose ps
```

### 配置API密钥

编辑 `.env` 文件，添加您的API密钥：

```bash
# 替换为实际的API密钥
OPENAI_API_KEY=sk-your-openai-api-key
CLAUDE_API_KEY=your-claude-api-key
```

## 📖 系统架构

### 整体架构图

```mermaid
graph TB
    subgraph "用户层"
        WebUI[Web管理界面]
        MobileApp[移动端应用]
        API[第三方API调用]
    end

    subgraph "网关层"
        Gateway[Spring Cloud Gateway]
        Auth[认证授权服务]
    end

    subgraph "业务服务层"
        DocService[文档管理服务]
        ParseService[文档解析服务]
        VectorService[向量化服务]
        SearchService[检索服务]
        QAService[问答服务]
    end

    subgraph "数据存储层"
        MySQL[(MySQL 8.0)]
        Redis[(Redis 7.0)]
        Milvus[(Milvus 2.3)]
        MinIO[(MinIO对象存储)]
    end

    WebUI --> Gateway
    Gateway --> Auth
    Gateway --> DocService
    DocService --> ParseService
    ParseService --> VectorService
    VectorService --> Milvus
    SearchService --> Milvus
    QAService --> SearchService
```

### 核心流程

1. **文档上传** → 格式检测 → 内容解析 → OCR识别
2. **文本预处理** → 智能分块 → 摘要提取 → 关键词提取
3. **向量化处理** → Embedding生成 → Milvus存储
4. **智能检索** → 向量检索 + 关键词检索 → 重排序
5. **问答生成** → 上下文构建 → LLM生成 → 流式输出

## 📊 性能指标

| 指标类型 | 目标值 | 说明 |
|----------|--------|------|
| 文档解析速度 | 1MB/秒 | 平均处理速度 |
| 向量化处理 | 1000条/分钟 | 文本块向量化速度 |
| 检索响应时间 | <500ms | 95%请求响应时间 |
| 问答响应时间 | <3秒 | 包含LLM调用时间 |
| 系统并发量 | 1000 QPS | 峰值并发处理能力 |
| 检索准确率 | >80% | Top-5检索结果相关性 |
| 问答准确率 | >85% | 基于人工评估 |

## 🛠️ 开发指南

### 项目结构

```
rag-knowledge-system/
├── 01-系统整体架构设计.md          # 系统架构设计
├── 02-文档解析与预处理模块设计.md    # 文档处理模块
├── 03-向量化与检索模块设计.md        # 向量检索模块
├── 04-技术选型对比分析.md           # 技术选型分析
├── 05-问答服务与API网关设计.md      # 问答服务设计
├── 06-部署配置与实现指南.md         # 部署实施指南
├── 07-项目结构与核心代码模板.md     # 代码模板
├── 08-实施路线图与总结.md           # 实施计划
├── quick-start.sh                  # 快速启动脚本
├── docker-compose.infrastructure.yml # 基础设施配置
├── rag-common/                     # 公共模块
├── rag-gateway/                    # API网关服务
├── rag-document/                   # 文档管理服务
├── rag-parser/                     # 文档解析服务
├── rag-vector/                     # 向量化服务
├── rag-search/                     # 检索服务
├── rag-qa/                         # 问答服务
└── rag-web/                        # 前端应用
```

### 开发步骤

1. **阅读技术文档**：从 `01-系统整体架构设计.md` 开始
2. **搭建开发环境**：运行 `quick-start.sh` 启动基础服务
3. **开发公共模块**：实现 `rag-common` 中的基础组件
4. **按模块开发**：根据 `08-实施路线图与总结.md` 的计划逐步实现
5. **集成测试**：确保各模块间正常通信
6. **性能优化**：根据性能指标进行调优

### API文档

启动服务后，可通过以下地址访问API文档：

- **API网关**: http://localhost:8080/swagger-ui.html
- **文档服务**: http://localhost:8082/swagger-ui.html
- **检索服务**: http://localhost:8085/swagger-ui.html
- **问答服务**: http://localhost:8086/swagger-ui.html

## 🔧 配置说明

### 服务端口

| 服务 | 端口 | 说明 |
|------|------|------|
| API网关 | 8080 | 统一入口 |
| 认证服务 | 8081 | 用户认证 |
| 文档服务 | 8082 | 文档管理 |
| 解析服务 | 8083 | 文档解析 |
| 向量服务 | 8084 | 向量化处理 |
| 检索服务 | 8085 | 智能检索 |
| 问答服务 | 8086 | 智能问答 |
| 用户服务 | 8087 | 用户管理 |

### 基础设施端口

| 服务 | 端口 | 访问地址 |
|------|------|----------|
| MySQL | 3306 | localhost:3306 |
| Redis | 6379 | localhost:6379 |
| Milvus | 19530 | localhost:19530 |
| Milvus管理 | 19121 | http://localhost:19121 |
| MinIO | 9000/9001 | http://localhost:9001 |

## 📝 使用示例

### 文档上传

```bash
curl -X POST "http://localhost:8080/api/documents/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.pdf" \
  -F "knowledgeBaseId=default" \
  -F "title=测试文档"
```

### 智能检索

```bash
curl -X POST "http://localhost:8080/api/search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "人工智能的发展趋势",
    "knowledgeBaseId": "default",
    "topK": 5
  }'
```

### 智能问答

```bash
curl -X POST "http://localhost:8080/api/qa/ask" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "什么是RAG技术？",
    "knowledgeBaseId": "default",
    "stream": false
  }'
```

## 🐛 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查Docker服务状态
   docker-compose ps
   
   # 查看服务日志
   docker-compose logs [服务名]
   ```

2. **数据库连接失败**
   ```bash
   # 检查MySQL是否正常启动
   docker exec -it rag-mysql mysql -urag_user -prag_pass -e "SELECT 1"
   ```

3. **Milvus连接失败**
   ```bash
   # 检查Milvus状态
   curl http://localhost:19121/health
   ```

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f mysql
docker-compose logs -f milvus
docker-compose logs -f redis
```

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- **项目维护者**: RAG技术团队
- **邮箱**: <EMAIL>
- **文档**: 查看项目中的详细技术文档

## 🙏 致谢

感谢以下开源项目的支持：

- [Spring Boot](https://spring.io/projects/spring-boot)
- [Milvus](https://milvus.io/)
- [Apache RocketMQ](https://rocketmq.apache.org/)
- [Redis](https://redis.io/)
- [MySQL](https://www.mysql.com/)

---

**⭐ 如果这个项目对您有帮助，请给我们一个星标！**
