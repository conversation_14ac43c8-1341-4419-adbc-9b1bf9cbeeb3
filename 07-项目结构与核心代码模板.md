# 企业级RAG知识库系统 - 项目结构与核心代码模板

## 1. 项目整体结构

```
rag-knowledge-system/
├── README.md
├── docker-compose.yml
├── docker-compose.infrastructure.yml
├── docker-compose.services.yml
├── pom.xml                          # 父级POM文件
├── config/                          # 配置文件目录
│   ├── milvus.yaml
│   ├── broker.conf
│   └── application-docker.yml
├── sql/                            # 数据库初始化脚本
│   ├── init.sql
│   └── schema/
├── scripts/                        # 部署和运维脚本
│   ├── quick-start.sh
│   ├── build-all.sh
│   └── deploy.sh
├── docs/                          # 文档目录
│   ├── api/
│   └── deployment/
├── rag-common/                    # 公共模块
├── rag-gateway/                   # API网关服务
├── rag-auth/                      # 认证授权服务
├── rag-document/                  # 文档管理服务
├── rag-parser/                    # 文档解析服务
├── rag-vector/                    # 向量化服务
├── rag-search/                    # 检索服务
├── rag-qa/                        # 问答服务
├── rag-user/                      # 用户管理服务
├── rag-ocr-python/               # Python OCR服务
├── rag-nlp-python/               # Python NLP服务
├── rag-embedding-python/         # Python Embedding服务
└── rag-web/                      # 前端Web应用
```

## 2. 父级POM配置

```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.rag</groupId>
    <artifactId>rag-knowledge-system</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <name>RAG Knowledge System</name>
    <description>Enterprise RAG Knowledge Base System</description>

    <modules>
        <module>rag-common</module>
        <module>rag-gateway</module>
        <module>rag-auth</module>
        <module>rag-document</module>
        <module>rag-parser</module>
        <module>rag-vector</module>
        <module>rag-search</module>
        <module>rag-qa</module>
        <module>rag-user</module>
    </modules>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        
        <!-- Spring Boot版本 -->
        <spring-boot.version>3.2.0</spring-boot.version>
        <spring-cloud.version>2023.0.0</spring-cloud.version>
        
        <!-- 数据库相关 -->
        <mybatis-plus.version>3.5.4</mybatis-plus.version>
        <mysql.version>8.0.33</mysql.version>
        <redis.version>4.4.6</redis.version>
        
        <!-- 消息队列 -->
        <rocketmq.version>5.1.4</rocketmq.version>
        
        <!-- 向量数据库 -->
        <milvus.version>2.3.4</milvus.version>
        
        <!-- 工具类 -->
        <hutool.version>5.8.22</hutool.version>
        <fastjson.version>2.0.43</fastjson.version>
        <lombok.version>1.18.30</lombok.version>
        
        <!-- 文档处理 -->
        <tika.version>2.9.1</tika.version>
        <poi.version>5.2.4</poi.version>
        
        <!-- 任务调度 -->
        <xxl-job.version>2.4.0</xxl-job.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <!-- Spring Cloud BOM -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <!-- MyBatis Plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            
            <!-- RocketMQ -->
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq.version}</version>
            </dependency>
            
            <!-- Milvus -->
            <dependency>
                <groupId>io.milvus</groupId>
                <artifactId>milvus-sdk-java</artifactId>
                <version>${milvus.version}</version>
            </dependency>
            
            <!-- 工具类 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            
            <!-- 文档处理 -->
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-core</artifactId>
                <version>${tika.version}</version>
            </dependency>
            
            <!-- XXL-Job -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <configuration>
                        <excludes>
                            <exclude>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                            </exclude>
                        </excludes>
                    </configuration>
                </plugin>
                
                <plugin>
                    <groupId>com.spotify</groupId>
                    <artifactId>dockerfile-maven-plugin</artifactId>
                    <version>1.4.13</version>
                    <configuration>
                        <repository>${project.artifactId}</repository>
                        <tag>${project.version}</tag>
                        <buildArgs>
                            <JAR_FILE>target/${project.build.finalName}.jar</JAR_FILE>
                        </buildArgs>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
```

## 3. 公共模块结构

```
rag-common/
├── pom.xml
└── src/main/java/com/rag/common/
    ├── config/                    # 公共配置
    │   ├── RedisConfig.java
    │   ├── MybatisPlusConfig.java
    │   └── RocketMQConfig.java
    ├── constant/                  # 常量定义
    │   ├── RocketMQTopics.java
    │   ├── RedisKeys.java
    │   └── ErrorCodes.java
    ├── dto/                      # 数据传输对象
    │   ├── request/
    │   ├── response/
    │   └── message/
    ├── entity/                   # 实体类
    │   ├── Document.java
    │   ├── User.java
    │   └── VectorEntity.java
    ├── enums/                    # 枚举类
    │   ├── DocumentStatus.java
    │   ├── DocumentFormat.java
    │   └── UserRole.java
    ├── exception/                # 异常类
    │   ├── BusinessException.java
    │   ├── SystemException.java
    │   └── GlobalExceptionHandler.java
    ├── util/                     # 工具类
    │   ├── JsonUtil.java
    │   ├── DateUtil.java
    │   └── FileUtil.java
    └── annotation/               # 自定义注解
        ├── RateLimiter.java
        └── LogOperation.java
```

## 4. 核心实体类模板

### 4.1 文档实体类

```java
// rag-common/src/main/java/com/rag/common/entity/Document.java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("rag_document")
public class Document {
    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    @TableField("file_name")
    private String fileName;
    
    @TableField("file_path")
    private String filePath;
    
    @TableField("file_size")
    private Long fileSize;
    
    @TableField("file_hash")
    private String fileHash;
    
    @TableField("mime_type")
    private String mimeType;
    
    @TableField("document_format")
    @EnumValue
    private DocumentFormat documentFormat;
    
    @TableField("status")
    @EnumValue
    private DocumentStatus status;
    
    @TableField("knowledge_base_id")
    private String knowledgeBaseId;
    
    @TableField("upload_user_id")
    private String uploadUserId;
    
    @TableField("title")
    private String title;
    
    @TableField("summary")
    private String summary;
    
    @TableField("page_count")
    private Integer pageCount;
    
    @TableField("chunk_count")
    private Integer chunkCount;
    
    @TableField("vector_count")
    private Integer vectorCount;
    
    @TableField("metadata")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> metadata;
    
    @TableField("parse_error_msg")
    private String parseErrorMsg;
    
    @TableField("created_time")
    private LocalDateTime createdTime;
    
    @TableField("updated_time")
    private LocalDateTime updatedTime;
    
    @TableField("created_by")
    private String createdBy;
    
    @TableField("updated_by")
    private String updatedBy;
    
    @TableField(exist = false)
    private List<TextChunk> chunks;
}
```

### 4.2 文本块实体类

```java
// rag-common/src/main/java/com/rag/common/entity/TextChunk.java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("rag_text_chunk")
public class TextChunk {
    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    @TableField("document_id")
    private String documentId;
    
    @TableField("chunk_index")
    private Integer chunkIndex;
    
    @TableField("page_number")
    private Integer pageNumber;
    
    @TableField("content")
    private String content;
    
    @TableField("content_hash")
    private String contentHash;
    
    @TableField("token_count")
    private Integer tokenCount;
    
    @TableField("start_position")
    private Integer startPosition;
    
    @TableField("end_position")
    private Integer endPosition;
    
    @TableField("metadata")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> metadata;
    
    @TableField("vector_id")
    private String vectorId;
    
    @TableField("embedding_model")
    private String embeddingModel;
    
    @TableField("created_time")
    private LocalDateTime createdTime;
    
    @TableField("updated_time")
    private LocalDateTime updatedTime;
}
```

## 5. 核心DTO模板

### 5.1 文档上传请求

```java
// rag-common/src/main/java/com/rag/common/dto/request/DocumentUploadRequest.java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentUploadRequest {
    
    @NotBlank(message = "知识库ID不能为空")
    private String knowledgeBaseId;
    
    @NotNull(message = "文件不能为空")
    private MultipartFile file;
    
    private String title;
    
    private String description;
    
    private Map<String, Object> metadata;
    
    @Builder.Default
    private Boolean autoProcess = true;
    
    @Builder.Default
    private String embeddingModel = "bge-large-zh";
    
    private List<String> tags;
}
```

### 5.2 检索请求

```java
// rag-common/src/main/java/com/rag/common/dto/request/SearchRequest.java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchRequest {
    
    @NotBlank(message = "查询内容不能为空")
    private String query;
    
    private String knowledgeBaseId;
    
    @Builder.Default
    private Integer topK = 10;
    
    @Builder.Default
    private Double threshold = 0.6;
    
    @Builder.Default
    private SearchType searchType = SearchType.HYBRID;
    
    private Map<String, Object> filters;
    
    @Builder.Default
    private String embeddingModel = "bge-large-zh";
    
    @Builder.Default
    private Boolean enableRerank = true;
    
    private Long startTime;
}
```

### 5.3 问答请求

```java
// rag-common/src/main/java/com/rag/common/dto/request/QARequest.java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QARequest {
    
    @NotBlank(message = "问题不能为空")
    private String question;
    
    private String sessionId;
    
    private String knowledgeBaseId;
    
    @Builder.Default
    private String modelName = "gpt-4";
    
    @Builder.Default
    private Integer maxTokens = 2000;
    
    @Builder.Default
    private Double temperature = 0.7;
    
    @Builder.Default
    private Boolean stream = false;
    
    private Map<String, Object> userPreferences;
    
    private Map<String, Object> filters;
    
    @Builder.Default
    private Integer contextMaxChunks = 5;
}
```

## 6. 服务模块模板

### 6.1 文档服务POM

```xml
<!-- rag-document/pom.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>rag-knowledge-system</artifactId>
        <groupId>com.rag</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>rag-document</artifactId>
    <name>RAG Document Service</name>
    <description>Document Management Service</description>

    <dependencies>
        <!-- 公共模块 -->
        <dependency>
            <groupId>com.rag</groupId>
            <artifactId>rag-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <!-- Spring Boot Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        
        <!-- Spring Cloud -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-consul-discovery</artifactId>
        </dependency>
        
        <!-- 数据库 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        
        <!-- Redis -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        
        <!-- RocketMQ -->
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
        </dependency>
        
        <!-- MinIO -->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>8.5.7</version>
        </dependency>
        
        <!-- 文档处理 -->
        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-core</artifactId>
        </dependency>
        
        <!-- 工具类 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
```

### 6.2 Dockerfile模板

```dockerfile
# rag-document/Dockerfile
FROM openjdk:17-jre-slim

LABEL maintainer="rag-team"
LABEL description="RAG Document Service"

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建应用目录
WORKDIR /app

# 复制jar文件
ARG JAR_FILE=target/*.jar
COPY ${JAR_FILE} app.jar

# 创建日志目录
RUN mkdir -p /app/logs

# 设置JVM参数
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+PrintGCDetails -Xloggc:/app/logs/gc.log"

# 暴露端口
EXPOSE 8082

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8082/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

## 7. 配置文件模板

### 7.1 应用配置

```yaml
# rag-document/src/main/resources/application.yml
server:
  port: 8082
  servlet:
    context-path: /document

spring:
  application:
    name: rag-document
  
  profiles:
    active: dev
  
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${MYSQL_HOST:localhost}:3306/rag_db?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai
    username: ${MYSQL_USERNAME:rag_user}
    password: ${MYSQL_PASSWORD:rag_pass}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 300000
      connection-timeout: 20000
  
  redis:
    host: ${REDIS_HOST:localhost}
    port: 6379
    password: ${REDIS_PASSWORD:rag123456}
    database: 0
    lettuce:
      pool:
        max-active: 200
        max-idle: 50
        min-idle: 10
        max-wait: 3000ms
  
  cloud:
    consul:
      host: ${CONSUL_HOST:localhost}
      port: 8500
      discovery:
        service-name: ${spring.application.name}
        health-check-path: /actuator/health
        health-check-interval: 10s

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: assign_id
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# RocketMQ配置
rocketmq:
  name-server: ${ROCKETMQ_NAMESERVER:localhost:9876}
  producer:
    group: document-producer-group
    send-message-timeout: 3000
    retry-times-when-send-failed: 3

# MinIO配置
minio:
  endpoint: http://${MINIO_HOST:localhost}:9000
  access-key: ${MINIO_ACCESS_KEY:minioadmin}
  secret-key: ${MINIO_SECRET_KEY:minioadmin}
  bucket-name: rag-documents

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

# 日志配置
logging:
  level:
    com.rag: DEBUG
    org.springframework.cloud: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /app/logs/rag-document.log
```

---

**项目结构和核心代码模板已完成，可以开始具体的开发工作。**
